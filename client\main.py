#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
淘宝天猫秒杀程序 - GUI客户端主程序
功能：扫码登录获取cookie并传递给后端服务器
作者：T团队
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import requests
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from client.gui.login_window import LoginWindow
from client.browser.cookie_manager import CookieManager
from client.utils.config import Config


class TaobaoSeckillClient:
    """淘宝秒杀客户端主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.config = Config()
        self.cookie_manager = CookieManager()
        self.setup_ui()
        self.setup_style()
        
    def setup_ui(self):
        """设置UI界面"""
        self.root.title("淘宝天猫秒杀程序 - 客户端 v2.0")
        self.root.geometry("800x600")
        self.root.resizable(False, False)
        
        # 设置窗口居中
        self.center_window()
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="20")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(
            self.main_frame, 
            text="淘宝天猫秒杀程序", 
            font=("SF Pro Display", 24, "bold")
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 30))
        
        # 服务器连接状态
        self.setup_server_status()
        
        # 登录区域
        self.setup_login_area()
        
        # Cookie状态区域
        self.setup_cookie_status()
        
        # 操作按钮区域
        self.setup_action_buttons()
        
        # 日志区域
        self.setup_log_area()
        
    def setup_style(self):
        """设置现代化白色苹果风格"""
        style = ttk.Style()
        
        # 配置样式
        style.configure('Title.TLabel', font=('SF Pro Display', 16, 'bold'))
        style.configure('Subtitle.TLabel', font=('SF Pro Display', 12))
        style.configure('Success.TLabel', foreground='#34C759')
        style.configure('Error.TLabel', foreground='#FF3B30')
        style.configure('Warning.TLabel', foreground='#FF9500')
        
        # 按钮样式
        style.configure('Action.TButton', font=('SF Pro Display', 11))
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_server_status(self):
        """设置服务器连接状态区域"""
        status_frame = ttk.LabelFrame(self.main_frame, text="服务器状态", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        self.server_status_label = ttk.Label(
            status_frame, 
            text="未连接", 
            style='Error.TLabel'
        )
        self.server_status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.check_server_btn = ttk.Button(
            status_frame,
            text="检查连接",
            command=self.check_server_connection,
            style='Action.TButton'
        )
        self.check_server_btn.grid(row=0, column=1, padx=(10, 0))
        
    def setup_login_area(self):
        """设置登录区域"""
        login_frame = ttk.LabelFrame(self.main_frame, text="账号登录", padding="10")
        login_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        self.login_status_label = ttk.Label(
            login_frame, 
            text="未登录", 
            style='Warning.TLabel'
        )
        self.login_status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.login_btn = ttk.Button(
            login_frame,
            text="扫码登录",
            command=self.start_login,
            style='Action.TButton'
        )
        self.login_btn.grid(row=0, column=1, padx=(10, 0))
        
    def setup_cookie_status(self):
        """设置Cookie状态区域"""
        cookie_frame = ttk.LabelFrame(self.main_frame, text="Cookie状态", padding="10")
        cookie_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        self.cookie_status_label = ttk.Label(
            cookie_frame, 
            text="无Cookie数据", 
            style='Warning.TLabel'
        )
        self.cookie_status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.upload_cookie_btn = ttk.Button(
            cookie_frame,
            text="上传Cookie",
            command=self.upload_cookies,
            style='Action.TButton',
            state='disabled'
        )
        self.upload_cookie_btn.grid(row=0, column=1, padx=(10, 0))
        
    def setup_action_buttons(self):
        """设置操作按钮区域"""
        action_frame = ttk.Frame(self.main_frame)
        action_frame.grid(row=4, column=0, columnspan=2, pady=(0, 20))
        
        self.clear_cookie_btn = ttk.Button(
            action_frame,
            text="清除Cookie",
            command=self.clear_cookies,
            style='Action.TButton'
        )
        self.clear_cookie_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.refresh_btn = ttk.Button(
            action_frame,
            text="刷新状态",
            command=self.refresh_status,
            style='Action.TButton'
        )
        self.refresh_btn.grid(row=0, column=1, padx=(0, 10))
        
        self.open_web_btn = ttk.Button(
            action_frame,
            text="打开Web管理",
            command=self.open_web_management,
            style='Action.TButton'
        )
        self.open_web_btn.grid(row=0, column=2)
        
    def setup_log_area(self):
        """设置日志区域"""
        log_frame = ttk.LabelFrame(self.main_frame, text="操作日志", padding="10")
        log_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 0))
        
        # 创建文本框和滚动条
        self.log_text = tk.Text(
            log_frame, 
            height=12, 
            width=70,
            font=('SF Mono', 10),
            bg='#F8F9FA',
            fg='#333333',
            wrap=tk.WORD
        )
        
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置网格权重
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 初始日志
        self.add_log("程序启动成功")
        
    def add_log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        
    def check_server_connection(self):
        """检查服务器连接"""
        def check():
            try:
                response = requests.get(
                    f"{self.config.server_url}/api/health", 
                    timeout=5
                )
                if response.status_code == 200:
                    self.server_status_label.config(text="已连接", style='Success.TLabel')
                    self.add_log("服务器连接正常")
                else:
                    self.server_status_label.config(text="连接异常", style='Error.TLabel')
                    self.add_log(f"服务器响应异常: {response.status_code}")
            except Exception as e:
                self.server_status_label.config(text="连接失败", style='Error.TLabel')
                self.add_log(f"服务器连接失败: {str(e)}")
                
        threading.Thread(target=check, daemon=True).start()
        
    def start_login(self):
        """开始登录流程"""
        self.add_log("启动登录窗口...")
        login_window = LoginWindow(self.root, self.on_login_success)
        
    def on_login_success(self, cookies):
        """登录成功回调"""
        self.cookie_manager.save_cookies(cookies)
        self.login_status_label.config(text="登录成功", style='Success.TLabel')
        self.cookie_status_label.config(text="Cookie已获取", style='Success.TLabel')
        self.upload_cookie_btn.config(state='normal')
        self.add_log("登录成功，Cookie已保存")
        
    def upload_cookies(self):
        """上传Cookie到服务器"""
        def upload():
            try:
                cookies = self.cookie_manager.load_cookies()
                if not cookies:
                    self.add_log("没有可上传的Cookie数据")
                    return
                    
                response = requests.post(
                    f"{self.config.server_url}/api/cookies/upload",
                    json={"cookies": cookies},
                    timeout=10
                )
                
                if response.status_code == 200:
                    self.add_log("Cookie上传成功")
                    messagebox.showinfo("成功", "Cookie已成功上传到服务器")
                else:
                    self.add_log(f"Cookie上传失败: {response.text}")
                    messagebox.showerror("错误", "Cookie上传失败")
                    
            except Exception as e:
                self.add_log(f"Cookie上传异常: {str(e)}")
                messagebox.showerror("错误", f"Cookie上传异常: {str(e)}")
                
        threading.Thread(target=upload, daemon=True).start()
        
    def clear_cookies(self):
        """清除Cookie"""
        self.cookie_manager.clear_cookies()
        self.login_status_label.config(text="未登录", style='Warning.TLabel')
        self.cookie_status_label.config(text="无Cookie数据", style='Warning.TLabel')
        self.upload_cookie_btn.config(state='disabled')
        self.add_log("Cookie已清除")
        
    def refresh_status(self):
        """刷新状态"""
        self.check_server_connection()
        cookies = self.cookie_manager.load_cookies()
        if cookies:
            self.cookie_status_label.config(text="Cookie已获取", style='Success.TLabel')
            self.upload_cookie_btn.config(state='normal')
        else:
            self.cookie_status_label.config(text="无Cookie数据", style='Warning.TLabel')
            self.upload_cookie_btn.config(state='disabled')
        self.add_log("状态已刷新")
        
    def open_web_management(self):
        """打开Web管理界面"""
        import webbrowser
        webbrowser.open(self.config.server_url)
        self.add_log("已打开Web管理界面")
        
    def run(self):
        """运行程序"""
        # 启动时检查服务器连接
        self.check_server_connection()
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(5, weight=1)
        
        # 启动主循环
        self.root.mainloop()


if __name__ == "__main__":
    app = TaobaoSeckillClient()
    app.run()
