/* 淘宝天猫秒杀程序 - 现代化白色苹果风格样式 */

:root {
    --primary-color: #007AFF;
    --success-color: #34C759;
    --warning-color: #FF9500;
    --danger-color: #FF3B30;
    --info-color: #5AC8FA;
    --light-color: #F2F2F7;
    --dark-color: #1C1C1E;
    --gray-color: #8E8E93;
    --sidebar-width: 260px;
    --sidebar-collapsed-width: 70px;
    --navbar-height: 60px;
    --border-radius: 12px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #FAFAFA;
    color: var(--dark-color);
    line-height: 1.6;
}

/* 侧边导航栏 */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: linear-gradient(180deg, #FFFFFF 0%, #F8F9FA 100%);
    border-right: 1px solid #E5E5EA;
    transition: var(--transition);
    z-index: 1000;
    overflow-y: auto;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #E5E5EA;
    text-align: center;
}

.sidebar-header h4 {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.sidebar.collapsed .sidebar-header h4 {
    font-size: 0;
}

.sidebar-menu {
    list-style: none;
    padding: 20px 0;
}

.menu-item {
    margin-bottom: 5px;
}

.menu-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--dark-color);
    text-decoration: none;
    transition: var(--transition);
    border-radius: 0;
    position: relative;
}

.menu-link:hover {
    background-color: rgba(0, 122, 255, 0.1);
    color: var(--primary-color);
}

.menu-link.active {
    background-color: var(--primary-color);
    color: white;
}

.menu-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: white;
}

.menu-link i {
    font-size: 18px;
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

.sidebar.collapsed .menu-link span {
    display: none;
}

.sidebar.collapsed .menu-link {
    justify-content: center;
    padding: 12px;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    border-top: 1px solid #E5E5EA;
    background-color: white;
}

.system-status {
    text-align: center;
}

.status-item {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    font-size: 12px;
    color: var(--gray-color);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-online {
    background-color: var(--success-color);
}

.status-time {
    font-size: 11px;
    color: var(--gray-color);
}

.sidebar.collapsed .sidebar-footer {
    display: none;
}

/* 主内容区域 */
.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    transition: var(--transition);
}

.main-content.expanded {
    margin-left: var(--sidebar-collapsed-width);
}

/* 顶部导航栏 */
.top-navbar {
    height: var(--navbar-height);
    background: white;
    border-bottom: 1px solid #E5E5EA;
    position: sticky;
    top: 0;
    z-index: 999;
}

.navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 24px;
}

.navbar-left {
    display: flex;
    align-items: center;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 20px;
    color: var(--gray-color);
    margin-right: 16px;
    padding: 8px;
    border-radius: 8px;
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background-color: var(--light-color);
    color: var(--dark-color);
}

.page-title {
    font-weight: 600;
    color: var(--dark-color);
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.navbar-item {
    font-size: 14px;
}

/* 页面内容 */
.page-content {
    padding: 24px;
}

/* 卡片样式 */
.card {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: none;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid #E5E5EA;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    padding: 16px 20px;
}

.card-body {
    padding: 20px;
}

/* 统计卡片 */
.stats-card {
    background: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%);
}

.stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.stats-number {
    font-size: 28px;
    font-weight: 700;
    color: var(--dark-color);
    line-height: 1;
}

.stats-label {
    font-size: 14px;
    color: var(--gray-color);
    margin-top: 4px;
}

/* 按钮样式 */
.btn {
    border-radius: 10px;
    font-weight: 500;
    padding: 10px 20px;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
}

.btn-primary:hover {
    background-color: #0056CC;
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 122, 255, 0.4);
}

.btn-success {
    background-color: var(--success-color);
    box-shadow: 0 4px 15px rgba(52, 199, 89, 0.3);
}

.btn-warning {
    background-color: var(--warning-color);
    box-shadow: 0 4px 15px rgba(255, 149, 0, 0.3);
}

.btn-info {
    background-color: var(--info-color);
    box-shadow: 0 4px 15px rgba(90, 200, 250, 0.3);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

/* 表格样式 */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th {
    background-color: var(--light-color);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 16px;
}

.table td {
    border: none;
    padding: 16px;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 122, 255, 0.05);
}

/* 徽章样式 */
.badge {
    border-radius: 20px;
    padding: 6px 12px;
    font-weight: 500;
    font-size: 12px;
}

/* 日志容器 */
.log-container {
    max-height: 300px;
    overflow-y: auto;
    background-color: #F8F9FA;
    border-radius: 8px;
    padding: 16px;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
}

/* 状态指示器 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-success {
    background-color: rgba(52, 199, 89, 0.1);
    color: var(--success-color);
}

.status-warning {
    background-color: rgba(255, 149, 0, 0.1);
    color: var(--warning-color);
}

.status-danger {
    background-color: rgba(255, 59, 48, 0.1);
    color: var(--danger-color);
}

.status-info {
    background-color: rgba(90, 200, 250, 0.1);
    color: var(--info-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .navbar-content {
        padding: 0 16px;
    }
    
    .page-content {
        padding: 16px;
    }
    
    .stats-number {
        font-size: 24px;
    }
    
    .stats-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
