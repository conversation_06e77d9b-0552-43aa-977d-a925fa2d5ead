#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
秒杀核心引擎
功能：基于原始版本思路，实现多线程、多任务的秒杀核心功能
"""

import threading
import time
import queue
import requests
import json
import re
from datetime import datetime
from urllib.parse import parse_qs, urlparse, quote
import logging
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)


class SeckillEngine:
    """秒杀引擎"""
    
    def __init__(self, db, cookie_model, task_model, log_model):
        self.db = db
        self.cookie_model = cookie_model
        self.task_model = task_model
        self.log_model = log_model
        
        self.is_running = False
        self.task_queue = queue.Queue()
        self.worker_threads = []
        self.max_workers = 10
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'success_tasks': 0,
            'failed_tasks': 0,
            'running_tasks': 0
        }
        
    def start(self):
        """启动秒杀引擎"""
        if self.is_running:
            return
            
        self.is_running = True
        logger.info("秒杀引擎启动")
        
        # 启动任务调度线程
        scheduler_thread = threading.Thread(target=self._task_scheduler, daemon=True)
        scheduler_thread.start()
        
        # 启动工作线程
        for i in range(self.max_workers):
            worker_thread = threading.Thread(target=self._worker, daemon=True)
            worker_thread.start()
            self.worker_threads.append(worker_thread)
            
        logger.info(f"秒杀引擎已启动，工作线程数: {self.max_workers}")
        
    def stop(self):
        """停止秒杀引擎"""
        self.is_running = False
        logger.info("秒杀引擎已停止")
        
    def _task_scheduler(self):
        """任务调度器"""
        while self.is_running:
            try:
                # 获取待执行任务
                pending_tasks = self.task_model.get_pending_tasks(limit=50)
                
                for task in pending_tasks:
                    # 检查任务是否到执行时间
                    if task['scheduled_time'] <= datetime.now():
                        # 将任务加入队列
                        self.task_queue.put(task)
                        
                        # 更新任务状态为运行中
                        self.task_model.update_task_status(task['task_id'], 'running')
                        
                        self.log_model.add_log('INFO', f'任务加入执行队列: {task["product_url"]}',
                                             task_id=task['task_id'], user_id=task['user_id'])
                        
                # 每5秒检查一次
                time.sleep(5)
                
            except Exception as e:
                logger.error(f"任务调度器出错: {e}")
                time.sleep(10)
                
    def _worker(self):
        """工作线程"""
        while self.is_running:
            try:
                # 从队列获取任务
                task = self.task_queue.get(timeout=1)
                
                self.stats['running_tasks'] += 1
                self.stats['total_tasks'] += 1
                
                # 执行秒杀任务
                success = self._execute_seckill_task(task)
                
                self.stats['running_tasks'] -= 1
                if success:
                    self.stats['success_tasks'] += 1
                else:
                    self.stats['failed_tasks'] += 1
                    
                self.task_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"工作线程出错: {e}")
                self.stats['running_tasks'] -= 1
                self.stats['failed_tasks'] += 1
                
    def _execute_seckill_task(self, task):
        """执行秒杀任务"""
        task_id = task['task_id']
        user_id = task['user_id']
        product_url = task['product_url']
        
        try:
            # 获取用户Cookie
            cookie_data = self.cookie_model.get_valid_cookies(user_id)
            if not cookie_data:
                self.log_model.add_log('ERROR', '用户Cookie无效或已过期',
                                     task_id=task_id, user_id=user_id)
                self.task_model.update_task_status(task_id, 'failed', 
                                                 error_message='Cookie无效或已过期')
                return False
                
            cookies = cookie_data['cookies']
            
            # 创建会话
            session = requests.Session()
            
            # 设置Cookie
            for name, value in cookies.items():
                session.cookies.set(name, value)
                
            # 设置请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
            
            # 执行秒杀流程
            success = self._perform_seckill(session, headers, product_url, task_id, user_id)
            
            if success:
                self.task_model.update_task_status(task_id, 'success', 
                                                 result={'message': '秒杀成功'})
                self.log_model.add_log('INFO', '秒杀任务执行成功',
                                     task_id=task_id, user_id=user_id)
            else:
                self.task_model.update_task_status(task_id, 'failed',
                                                 error_message='秒杀执行失败')
                self.log_model.add_log('ERROR', '秒杀任务执行失败',
                                     task_id=task_id, user_id=user_id)
                
            return success
            
        except Exception as e:
            logger.error(f"执行秒杀任务出错: {e}")
            self.task_model.update_task_status(task_id, 'failed', error_message=str(e))
            self.log_model.add_log('ERROR', f'秒杀任务执行异常: {str(e)}',
                                 task_id=task_id, user_id=user_id)
            return False
            
    def _perform_seckill(self, session, headers, product_url, task_id, user_id):
        """执行秒杀流程（基于原始版本逻辑）"""
        try:
            # 1. 获取商品页面
            self.log_model.add_log('INFO', f'正在访问商品页面: {product_url}',
                                 task_id=task_id, user_id=user_id)
            
            response = session.get(product_url, headers=headers, timeout=10)
            if response.status_code != 200:
                self.log_model.add_log('ERROR', f'访问商品页面失败: {response.status_code}',
                                     task_id=task_id, user_id=user_id)
                return False
                
            page_content = response.text
            
            # 2. 解析商品信息
            product_info = self._parse_product_info(page_content, product_url)
            if not product_info:
                self.log_model.add_log('ERROR', '解析商品信息失败',
                                     task_id=task_id, user_id=user_id)
                return False
                
            # 3. 构建购买参数
            buy_params = self._build_buy_params(page_content, product_info, session.cookies)
            if not buy_params:
                self.log_model.add_log('ERROR', '构建购买参数失败',
                                     task_id=task_id, user_id=user_id)
                return False
                
            # 4. 提交订单确认
            self.log_model.add_log('INFO', '正在提交订单确认...',
                                 task_id=task_id, user_id=user_id)
            
            confirm_result = self._confirm_order(session, headers, buy_params, product_url)
            if not confirm_result:
                self.log_model.add_log('ERROR', '订单确认失败',
                                     task_id=task_id, user_id=user_id)
                return False
                
            # 5. 提交最终订单
            self.log_model.add_log('INFO', '正在提交最终订单...',
                                 task_id=task_id, user_id=user_id)
            
            submit_result = self._submit_order(session, headers, confirm_result)
            if submit_result:
                self.log_model.add_log('INFO', '订单提交成功！',
                                     task_id=task_id, user_id=user_id)
                return True
            else:
                self.log_model.add_log('ERROR', '订单提交失败',
                                     task_id=task_id, user_id=user_id)
                return False
                
        except Exception as e:
            logger.error(f"秒杀流程执行出错: {e}")
            self.log_model.add_log('ERROR', f'秒杀流程异常: {str(e)}',
                                 task_id=task_id, user_id=user_id)
            return False
            
    def _parse_product_info(self, page_content, product_url):
        """解析商品信息"""
        try:
            # 解析商品ID和SKU ID
            url_match = re.search(r'id=(\d+).*?&skuId=(\d+)', product_url)
            if not url_match:
                return None
                
            item_id = url_match.group(1)
            sku_id = url_match.group(2)
            
            # 解析其他必要信息
            seller_id_match = re.search(r'"sellerId":"(\d+)"', page_content)
            seller_id = seller_id_match.group(1) if seller_id_match else ""
            
            price_match = re.search(r'"price":"(\d+\.\d+)"', page_content)
            price = price_match.group(1) if price_match else "0.00"
            
            return {
                'item_id': item_id,
                'sku_id': sku_id,
                'seller_id': seller_id,
                'price': price
            }
            
        except Exception as e:
            logger.error(f"解析商品信息出错: {e}")
            return None
            
    def _build_buy_params(self, page_content, product_info, cookies):
        """构建购买参数"""
        try:
            # 获取必要的表单参数
            tb_token = cookies.get('_tb_token_', '')
            
            # 构建购买参数
            buy_param = f"{product_info['item_id']}_1_{product_info['sku_id']}"
            
            params = {
                'item_id': product_info['item_id'],
                'sku_id': product_info['sku_id'],
                'seller_id': product_info['seller_id'],
                'quantity': '1',
                'buy_param': buy_param,
                '_tb_token_': tb_token,
                'buy_now': product_info['price'],
                'current_price': product_info['price']
            }
            
            return params
            
        except Exception as e:
            logger.error(f"构建购买参数出错: {e}")
            return None
            
    def _confirm_order(self, session, headers, buy_params, referer_url):
        """确认订单"""
        try:
            confirm_url = "https://buy.tmall.com/order/confirm_order.htm"
            
            headers_copy = headers.copy()
            headers_copy.update({
                'Origin': 'https://detail.tmall.com',
                'Referer': referer_url,
                'Content-Type': 'application/x-www-form-urlencoded'
            })
            
            response = session.post(confirm_url, headers=headers_copy, 
                                  data=buy_params, timeout=15)
            
            if response.status_code == 200:
                # 解析确认页面，提取提交订单所需的参数
                return self._parse_confirm_response(response.text)
            else:
                return None
                
        except Exception as e:
            logger.error(f"确认订单出错: {e}")
            return None
            
    def _parse_confirm_response(self, response_text):
        """解析确认订单响应"""
        try:
            # 查找订单数据
            order_data_match = re.search(r'var orderData= ({.*?});', response_text, re.DOTALL)
            if not order_data_match:
                return None
                
            order_data_str = order_data_match.group(1)
            order_data = json.loads(order_data_str)
            
            # 提取提交订单所需的参数
            submit_data = {}
            if 'data' in order_data and 'submitOrderPC_1' in order_data['data']:
                submit_info = order_data['data']['submitOrderPC_1']
                if 'hidden' in submit_info and 'extensionMap' in submit_info['hidden']:
                    ext_map = submit_info['hidden']['extensionMap']
                    submit_data = {
                        'action': ext_map.get('action', ''),
                        'event_submit_do_confirm': ext_map.get('event_submit_do_confirm', ''),
                        'secretValue': ext_map.get('secretValue', ''),
                        'sparam1': ext_map.get('sparam1', '')
                    }
                    
            return submit_data if submit_data else None
            
        except Exception as e:
            logger.error(f"解析确认订单响应出错: {e}")
            return None
            
    def _submit_order(self, session, headers, submit_params):
        """提交最终订单"""
        try:
            submit_url = "https://buy.tmall.com/auction/confirm_order.htm"
            
            headers_copy = headers.copy()
            headers_copy.update({
                'Origin': 'https://buy.tmall.com',
                'Referer': 'https://buy.tmall.com/order/confirm_order.htm',
                'Content-Type': 'application/x-www-form-urlencoded'
            })
            
            response = session.post(submit_url, headers=headers_copy,
                                  data=submit_params, timeout=15)
            
            if response.status_code == 200:
                # 检查是否成功
                success_indicators = [
                    '正在创建支付宝安全链接',
                    '订单提交成功',
                    'alipay.com'
                ]
                
                for indicator in success_indicators:
                    if indicator in response.text:
                        return True
                        
            return False
            
        except Exception as e:
            logger.error(f"提交订单出错: {e}")
            return False
            
    def get_status(self):
        """获取引擎状态"""
        return {
            'is_running': self.is_running,
            'queue_size': self.task_queue.qsize(),
            'worker_count': len(self.worker_threads),
            'statistics': self.stats.copy()
        }
