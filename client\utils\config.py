#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
功能：管理程序配置信息
"""

import json
import os


class Config:
    """配置管理器"""
    
    def __init__(self):
        self.config_file = "config.json"
        self.default_config = {
            "server_url": "http://localhost:5000",
            "chrome_path": "./chrome-win64/chrome.exe",
            "chromedriver_path": "./chromedriver-win64/chromedriver.exe",
            "timeout": 30,
            "retry_count": 3,
            "log_level": "INFO"
        }
        
        self.config = self.load_config()
        
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                # 合并默认配置
                for key, value in self.default_config.items():
                    if key not in config:
                        config[key] = value
                        
                return config
            else:
                # 创建默认配置文件
                self.save_config(self.default_config)
                return self.default_config.copy()
                
        except Exception as e:
            print(f"加载配置失败: {e}")
            return self.default_config.copy()
            
    def save_config(self, config=None):
        """保存配置文件"""
        try:
            if config is None:
                config = self.config
                
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
            return True
            
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
            
    def get(self, key, default=None):
        """获取配置值"""
        return self.config.get(key, default)
        
    def set(self, key, value):
        """设置配置值"""
        self.config[key] = value
        return self.save_config()
        
    @property
    def server_url(self):
        """服务器URL"""
        return self.config.get("server_url", "http://localhost:5000")
        
    @property
    def chrome_path(self):
        """Chrome浏览器路径"""
        return self.config.get("chrome_path", "./chrome-win64/chrome.exe")
        
    @property
    def chromedriver_path(self):
        """ChromeDriver路径"""
        return self.config.get("chromedriver_path", "./chromedriver-win64/chromedriver.exe")
        
    @property
    def timeout(self):
        """超时时间"""
        return self.config.get("timeout", 30)
        
    @property
    def retry_count(self):
        """重试次数"""
        return self.config.get("retry_count", 3)
