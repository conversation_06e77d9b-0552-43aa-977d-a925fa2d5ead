{% extends "base.html" %}

{% block title %}Cookie管理 - 淘宝天猫秒杀程序{% endblock %}
{% block page_title %}Cookie管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 操作栏 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <button class="btn btn-success" onclick="openClientApp()">
                                    <i class="bi bi-upload"></i> 打开客户端上传
                                </button>
                                <button class="btn btn-outline-secondary" onclick="refreshCookies()">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                                <button class="btn btn-outline-warning" onclick="checkAllCookies()">
                                    <i class="bi bi-check-circle"></i> 检查有效性
                                </button>
                                <button class="btn btn-outline-danger" onclick="cleanExpiredCookies()">
                                    <i class="bi bi-trash"></i> 清理过期
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <select class="form-select" id="statusFilter" onchange="filterCookies()">
                                    <option value="">全部状态</option>
                                    <option value="valid">有效</option>
                                    <option value="expired">已过期</option>
                                    <option value="invalid">无效</option>
                                </select>
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索用户..." onkeyup="searchCookies()">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Cookie统计 -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-primary" id="totalCookies">0</div>
                    <div class="stats-label">总Cookie数</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-success" id="validCookies">0</div>
                    <div class="stats-label">有效Cookie</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-warning" id="expiringSoon">0</div>
                    <div class="stats-label">即将过期</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-danger" id="expiredCookies">0</div>
                    <div class="stats-label">已过期</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 使用说明 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> Cookie管理说明</h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="mb-0">
                            <li>Cookie是执行秒杀任务的必要凭证</li>
                            <li>每个用户需要通过客户端程序上传Cookie</li>
                            <li>Cookie有效期通常为7天，需要定期更新</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="mb-0">
                            <li>系统会自动检查Cookie有效性</li>
                            <li>过期的Cookie会影响秒杀任务执行</li>
                            <li>建议定期清理无效和过期的Cookie</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Cookie列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-key"></i> Cookie列表
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th width="5%">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th width="15%">用户信息</th>
                                    <th width="10%">状态</th>
                                    <th width="15%">创建时间</th>
                                    <th width="15%">过期时间</th>
                                    <th width="10%">使用次数</th>
                                    <th width="15%">最后使用</th>
                                    <th width="10%">有效性</th>
                                    <th width="5%">操作</th>
                                </tr>
                            </thead>
                            <tbody id="cookieTableBody">
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <div class="loading"></div>
                                        <div class="mt-2">正在加载Cookie数据...</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted">
                            显示 <span id="showingCount">0</span> 条，共 <span id="totalCount">0</span> 条记录
                        </div>
                        <nav>
                            <ul class="pagination pagination-sm mb-0" id="pagination">
                                <!-- 分页按钮将通过JavaScript生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cookie详情模态框 -->
<div class="modal fade" id="cookieDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-key"></i> Cookie详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="cookieDetailContent">
                <!-- Cookie详情内容将通过JavaScript填充 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-warning" id="testCookieBtn" onclick="testCurrentCookie()">
                    <i class="bi bi-check-circle"></i> 测试有效性
                </button>
                <button type="button" class="btn btn-danger" id="deleteCookieBtn" onclick="deleteCurrentCookie()">
                    <i class="bi bi-trash"></i> 删除Cookie
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 上传说明模态框 -->
<div class="modal fade" id="uploadHelpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-question-circle"></i> Cookie上传说明
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>步骤说明</h6>
                        <ol>
                            <li>下载并运行客户端程序</li>
                            <li>点击"扫码登录"按钮</li>
                            <li>使用手机淘宝扫描二维码</li>
                            <li>登录成功后自动获取Cookie</li>
                            <li>点击"上传Cookie"按钮</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6>注意事项</h6>
                        <ul>
                            <li>确保网络连接正常</li>
                            <li>使用真实的淘宝账号登录</li>
                            <li>不要在登录过程中关闭浏览器</li>
                            <li>Cookie上传成功后会自动显示在列表中</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-warning mt-3">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>安全提醒：</strong>
                    Cookie包含敏感的登录信息，请确保在安全的环境中操作，不要将Cookie信息泄露给他人。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">我知道了</button>
                <button type="button" class="btn btn-primary" onclick="downloadClient()">
                    <i class="bi bi-download"></i> 下载客户端
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentCookies = [];
let currentPage = 1;
let pageSize = 20;
let currentCookieId = null;

// 页面加载完成后初始化
$(document).ready(function() {
    loadCookies();
    
    // 每60秒刷新一次Cookie状态
    setInterval(refreshCookieStatus, 60000);
});

// 加载Cookie列表
function loadCookies() {
    const tbody = document.getElementById('cookieTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="9" class="text-center py-4">
                <div class="loading"></div>
                <div class="mt-2">正在加载Cookie数据...</div>
            </td>
        </tr>
    `;

    // 调用API获取Cookie列表
    fetch('/api/cookies')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.cookies) {
                currentCookies = data.cookies;
                renderCookies(data.cookies);
                updateCookieStats(data.cookies);
            } else {
                currentCookies = [];
                renderCookies([]);
                updateCookieStats([]);
            }
        })
        .catch(error => {
            console.error('加载Cookie列表失败:', error);
            currentCookies = [];
            renderCookies([]);
            updateCookieStats([]);
        });
}

// 渲染Cookie列表
function renderCookies(cookies) {
    const tbody = document.getElementById('cookieTableBody');
    
    if (cookies.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center py-4 text-muted">
                    <i class="bi bi-key" style="font-size: 2rem;"></i>
                    <div class="mt-2">暂无Cookie数据</div>
                    <button class="btn btn-primary btn-sm mt-2" onclick="openClientApp()">
                        <i class="bi bi-upload"></i> 立即上传Cookie
                    </button>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = cookies.map(cookie => `
        <tr data-cookie-id="${cookie.cookie_id}">
            <td>
                <input type="checkbox" class="cookie-checkbox" value="${cookie.cookie_id}">
            </td>
            <td>
                <div class="user-info">
                    <div class="user-name fw-bold">${cookie.nickname || cookie.username}</div>
                    <div class="user-username text-muted small">@${cookie.username}</div>
                </div>
            </td>
            <td>
                ${createCookieStatusBadge(cookie)}
            </td>
            <td>
                <div class="time-info">
                    <div class="small">${Utils.formatDateTime(cookie.created_at)}</div>
                    <div class="text-muted small">${Utils.formatRelativeTime(cookie.created_at)}</div>
                </div>
            </td>
            <td>
                <div class="time-info">
                    <div class="small ${isExpiringSoon(cookie.expires_at) ? 'text-warning fw-bold' : ''}">${Utils.formatDateTime(cookie.expires_at)}</div>
                    <div class="text-muted small">${getExpiryStatus(cookie.expires_at)}</div>
                </div>
            </td>
            <td>
                <span class="badge ${cookie.use_count > 10 ? 'bg-success' : cookie.use_count > 5 ? 'bg-warning' : 'bg-light text-dark'}">${cookie.use_count}</span>
            </td>
            <td>
                <div class="time-info">
                    ${cookie.last_used ? `
                        <div class="small">${Utils.formatDateTime(cookie.last_used)}</div>
                        <div class="text-muted small">${Utils.formatRelativeTime(cookie.last_used)}</div>
                    ` : '<span class="text-muted">从未使用</span>'}
                </div>
            </td>
            <td>
                ${createValidityIndicator(cookie)}
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewCookieDetail('${cookie.cookie_id}')" title="查看详情">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-outline-warning" onclick="testCookie('${cookie.cookie_id}')" title="测试有效性">
                        <i class="bi bi-check-circle"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteCookie('${cookie.cookie_id}')" title="删除Cookie">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    // 更新显示计数
    document.getElementById('showingCount').textContent = cookies.length;
    document.getElementById('totalCount').textContent = cookies.length;
}

// 创建Cookie状态徽章
function createCookieStatusBadge(cookie) {
    const now = new Date();
    const expiresAt = new Date(cookie.expires_at);
    
    if (!cookie.is_valid) {
        return '<span class="badge bg-danger">无效</span>';
    } else if (expiresAt < now) {
        return '<span class="badge bg-secondary">已过期</span>';
    } else if (isExpiringSoon(cookie.expires_at)) {
        return '<span class="badge bg-warning">即将过期</span>';
    } else {
        return '<span class="badge bg-success">有效</span>';
    }
}

// 创建有效性指示器
function createValidityIndicator(cookie) {
    const now = new Date();
    const expiresAt = new Date(cookie.expires_at);
    
    if (!cookie.is_valid) {
        return '<i class="bi bi-x-circle text-danger" title="无效"></i>';
    } else if (expiresAt < now) {
        return '<i class="bi bi-clock text-secondary" title="已过期"></i>';
    } else if (isExpiringSoon(cookie.expires_at)) {
        return '<i class="bi bi-exclamation-triangle text-warning" title="即将过期"></i>';
    } else {
        return '<i class="bi bi-check-circle text-success" title="有效"></i>';
    }
}

// 检查是否即将过期（24小时内）
function isExpiringSoon(expiresAt) {
    const now = new Date();
    const expires = new Date(expiresAt);
    const timeDiff = expires - now;
    return timeDiff > 0 && timeDiff < 24 * 60 * 60 * 1000; // 24小时
}

// 获取过期状态文本
function getExpiryStatus(expiresAt) {
    const now = new Date();
    const expires = new Date(expiresAt);
    const timeDiff = expires - now;
    
    if (timeDiff < 0) {
        return '已过期';
    } else if (timeDiff < 24 * 60 * 60 * 1000) {
        const hours = Math.floor(timeDiff / (60 * 60 * 1000));
        return `${hours}小时后过期`;
    } else {
        const days = Math.floor(timeDiff / (24 * 60 * 60 * 1000));
        return `${days}天后过期`;
    }
}

// 更新Cookie统计
function updateCookieStats(cookies) {
    const now = new Date();
    
    const stats = {
        total: cookies.length,
        valid: cookies.filter(c => c.is_valid && new Date(c.expires_at) > now).length,
        expiringSoon: cookies.filter(c => c.is_valid && isExpiringSoon(c.expires_at)).length,
        expired: cookies.filter(c => !c.is_valid || new Date(c.expires_at) < now).length
    };
    
    document.getElementById('totalCookies').textContent = stats.total;
    document.getElementById('validCookies').textContent = stats.valid;
    document.getElementById('expiringSoon').textContent = stats.expiringSoon;
    document.getElementById('expiredCookies').textContent = stats.expired;
}

// 查看Cookie详情
function viewCookieDetail(cookieId) {
    currentCookieId = cookieId;
    const cookie = currentCookies.find(c => c.cookie_id === cookieId);
    
    if (!cookie) {
        Utils.showToast('Cookie不存在', 'error');
        return;
    }
    
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>Cookie ID:</td><td>${cookie.cookie_id}</td></tr>
                    <tr><td>用户:</td><td>${cookie.nickname || cookie.username}</td></tr>
                    <tr><td>状态:</td><td>${createCookieStatusBadge(cookie)}</td></tr>
                    <tr><td>有效性:</td><td>${createValidityIndicator(cookie)}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>使用统计</h6>
                <table class="table table-sm">
                    <tr><td>使用次数:</td><td>${cookie.use_count}</td></tr>
                    <tr><td>最后使用:</td><td>${cookie.last_used ? Utils.formatDateTime(cookie.last_used) : '从未使用'}</td></tr>
                </table>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-6">
                <h6>时间信息</h6>
                <table class="table table-sm">
                    <tr><td>创建时间:</td><td>${Utils.formatDateTime(cookie.created_at)}</td></tr>
                    <tr><td>过期时间:</td><td>${Utils.formatDateTime(cookie.expires_at)}</td></tr>
                    <tr><td>剩余时间:</td><td>${getExpiryStatus(cookie.expires_at)}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>安全信息</h6>
                <div class="alert alert-warning">
                    <i class="bi bi-shield-exclamation"></i>
                    <small>Cookie包含敏感信息，请妥善保管</small>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('cookieDetailContent').innerHTML = content;
    $('#cookieDetailModal').modal('show');
}

// 测试Cookie有效性
function testCookie(cookieId) {
    const cookie = currentCookies.find(c => c.cookie_id === cookieId);
    if (!cookie) return;
    
    Utils.showToast('正在测试Cookie有效性...', 'info');
    
    // 模拟测试过程
    setTimeout(() => {
        const isValid = Math.random() > 0.3; // 70%概率有效
        if (isValid) {
            Utils.showToast('Cookie测试通过，状态正常', 'success');
        } else {
            Utils.showToast('Cookie测试失败，可能已失效', 'error');
        }
    }, 2000);
}

// 测试当前Cookie
function testCurrentCookie() {
    if (currentCookieId) {
        $('#cookieDetailModal').modal('hide');
        setTimeout(() => testCookie(currentCookieId), 300);
    }
}

// 删除Cookie
function deleteCookie(cookieId) {
    const cookie = currentCookies.find(c => c.cookie_id === cookieId);
    if (!cookie) return;
    
    Utils.confirm(`确定要删除用户 "${cookie.username}" 的Cookie吗？`, () => {
        API.deleteCookies(cookie.user_id)
            .then(response => {
                if (response.success) {
                    Utils.showToast('Cookie删除成功', 'success');
                    loadCookies(); // 重新加载列表
                }
            })
            .catch(error => {
                Utils.showToast('删除Cookie失败', 'error');
            });
    });
}

// 删除当前Cookie
function deleteCurrentCookie() {
    if (currentCookieId) {
        $('#cookieDetailModal').modal('hide');
        setTimeout(() => deleteCookie(currentCookieId), 300);
    }
}

// 打开客户端应用
function openClientApp() {
    $('#uploadHelpModal').modal('show');
}

// 下载客户端
function downloadClient() {
    Utils.showToast('客户端下载功能开发中...', 'info');
}

// 刷新Cookie
function refreshCookies() {
    loadCookies();
    Utils.showToast('Cookie列表已刷新', 'info');
}

// 刷新Cookie状态
function refreshCookieStatus() {
    // 这里可以调用API获取最新的Cookie状态
    console.log('刷新Cookie状态');
}

// 检查所有Cookie
function checkAllCookies() {
    Utils.showToast('正在检查所有Cookie有效性...', 'info');
    
    // 模拟检查过程
    setTimeout(() => {
        Utils.showToast('Cookie有效性检查完成', 'success');
        loadCookies(); // 重新加载以显示最新状态
    }, 3000);
}

// 清理过期Cookie
function cleanExpiredCookies() {
    const expiredCount = currentCookies.filter(c => 
        !c.is_valid || new Date(c.expires_at) < new Date()
    ).length;
    
    if (expiredCount === 0) {
        Utils.showToast('没有需要清理的过期Cookie', 'info');
        return;
    }
    
    Utils.confirm(`发现 ${expiredCount} 个过期Cookie，确定要清理吗？`, () => {
        Utils.showToast('正在清理过期Cookie...', 'info');
        
        // 模拟清理过程
        setTimeout(() => {
            Utils.showToast(`已清理 ${expiredCount} 个过期Cookie`, 'success');
            loadCookies(); // 重新加载列表
        }, 2000);
    });
}

// 过滤Cookie
function filterCookies() {
    const status = document.getElementById('statusFilter').value;
    let filteredCookies = currentCookies;
    
    if (status === 'valid') {
        filteredCookies = currentCookies.filter(c => c.is_valid && new Date(c.expires_at) > new Date());
    } else if (status === 'expired') {
        filteredCookies = currentCookies.filter(c => !c.is_valid || new Date(c.expires_at) < new Date());
    } else if (status === 'invalid') {
        filteredCookies = currentCookies.filter(c => !c.is_valid);
    }
    
    renderCookies(filteredCookies);
}

// 搜索Cookie
function searchCookies() {
    const keyword = document.getElementById('searchInput').value.toLowerCase();
    const filteredCookies = keyword ? 
        currentCookies.filter(c => 
            c.username.toLowerCase().includes(keyword) || 
            (c.nickname && c.nickname.toLowerCase().includes(keyword))
        ) : currentCookies;
    renderCookies(filteredCookies);
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.cookie-checkbox');
    checkboxes.forEach(cb => cb.checked = selectAll.checked);
}
</script>
{% endblock %}
