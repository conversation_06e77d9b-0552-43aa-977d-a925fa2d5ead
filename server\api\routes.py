#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API路由定义
功能：定义所有API接口路由
"""

from flask import request, jsonify
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


def create_api_routes(app, user_model, cookie_model, task_model, product_model, log_model):
    """创建API路由"""
    
    # ==================== Cookie相关API ====================
    
    @app.route('/api/cookies/upload', methods=['POST'])
    def upload_cookies():
        """上传Cookie"""
        try:
            data = request.get_json()
            if not data or 'cookies' not in data:
                return jsonify({'error': '缺少Cookie数据'}), 400
                
            cookies = data['cookies']
            username = data.get('username', 'anonymous')
            
            # 创建或获取用户
            user = user_model.get_user_by_username(username)
            if not user:
                user_id = user_model.create_user(username)
            else:
                user_id = user['user_id']
                user_model.update_login_info(user_id)
                
            # 保存Cookie
            cookie_id = cookie_model.save_cookies(user_id, cookies)
            
            # 记录日志
            log_model.add_log('INFO', f'用户 {username} 上传了Cookie', user_id=user_id)
            
            return jsonify({
                'success': True,
                'message': 'Cookie上传成功',
                'cookie_id': cookie_id,
                'user_id': user_id
            })
            
        except Exception as e:
            logger.error(f"上传Cookie失败: {e}")
            return jsonify({'error': str(e)}), 500
            
    @app.route('/api/cookies/<user_id>', methods=['GET'])
    def get_user_cookies(user_id):
        """获取用户Cookie"""
        try:
            cookie_data = cookie_model.get_valid_cookies(user_id)
            if not cookie_data:
                return jsonify({'error': '未找到有效Cookie'}), 404
                
            return jsonify({
                'success': True,
                'cookie_id': cookie_data['cookie_id'],
                'created_at': cookie_data['created_at'].isoformat(),
                'expires_at': cookie_data['expires_at'].isoformat(),
                'use_count': cookie_data['use_count']
            })
            
        except Exception as e:
            logger.error(f"获取Cookie失败: {e}")
            return jsonify({'error': str(e)}), 500
            
    @app.route('/api/cookies/<user_id>', methods=['DELETE'])
    def delete_user_cookies(user_id):
        """删除用户Cookie"""
        try:
            success = cookie_model.invalidate_cookies(user_id)
            if success:
                log_model.add_log('INFO', f'用户 {user_id} 的Cookie已失效', user_id=user_id)
                return jsonify({'success': True, 'message': 'Cookie已删除'})
            else:
                return jsonify({'error': '删除失败'}), 400
                
        except Exception as e:
            logger.error(f"删除Cookie失败: {e}")
            return jsonify({'error': str(e)}), 500
            
    # ==================== 任务相关API ====================
    
    @app.route('/api/tasks', methods=['POST'])
    def create_task():
        """创建秒杀任务"""
        try:
            data = request.get_json()
            required_fields = ['user_id', 'product_url', 'scheduled_time']
            
            for field in required_fields:
                if field not in data:
                    return jsonify({'error': f'缺少必需字段: {field}'}), 400
                    
            # 解析时间
            try:
                scheduled_time = datetime.fromisoformat(data['scheduled_time'])
            except ValueError:
                return jsonify({'error': '时间格式错误'}), 400
                
            # 检查用户是否存在
            user = user_model.get_user(data['user_id'])
            if not user:
                return jsonify({'error': '用户不存在'}), 404
                
            # 检查是否有有效Cookie
            cookie_data = cookie_model.get_valid_cookies(data['user_id'])
            if not cookie_data:
                return jsonify({'error': '用户没有有效的Cookie'}), 400
                
            # 创建任务
            task_id = task_model.create_task(
                user_id=data['user_id'],
                product_url=data['product_url'],
                scheduled_time=scheduled_time,
                task_type=data.get('task_type', 'single'),
                quantity=data.get('quantity', 1)
            )
            
            # 记录日志
            log_model.add_log('INFO', f'创建秒杀任务: {data["product_url"]}', 
                            task_id=task_id, user_id=data['user_id'])
            
            return jsonify({
                'success': True,
                'message': '任务创建成功',
                'task_id': task_id
            })
            
        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            return jsonify({'error': str(e)}), 500
            
    @app.route('/api/tasks/<task_id>', methods=['GET'])
    def get_task(task_id):
        """获取任务详情"""
        try:
            task = task_model.get_task(task_id)
            if not task:
                return jsonify({'error': '任务不存在'}), 404
                
            # 转换时间格式
            task['created_at'] = task['created_at'].isoformat()
            task['updated_at'] = task['updated_at'].isoformat()
            task['scheduled_time'] = task['scheduled_time'].isoformat()
            
            if task['started_at']:
                task['started_at'] = task['started_at'].isoformat()
            if task['completed_at']:
                task['completed_at'] = task['completed_at'].isoformat()
                
            return jsonify({
                'success': True,
                'task': task
            })
            
        except Exception as e:
            logger.error(f"获取任务失败: {e}")
            return jsonify({'error': str(e)}), 500
            
    @app.route('/api/tasks/user/<user_id>', methods=['GET'])
    def get_user_tasks(user_id):
        """获取用户任务列表"""
        try:
            status = request.args.get('status')
            tasks = task_model.get_user_tasks(user_id, status)
            
            # 转换时间格式
            for task in tasks:
                task['created_at'] = task['created_at'].isoformat()
                task['updated_at'] = task['updated_at'].isoformat()
                task['scheduled_time'] = task['scheduled_time'].isoformat()
                
                if task['started_at']:
                    task['started_at'] = task['started_at'].isoformat()
                if task['completed_at']:
                    task['completed_at'] = task['completed_at'].isoformat()
                    
            return jsonify({
                'success': True,
                'tasks': tasks
            })
            
        except Exception as e:
            logger.error(f"获取用户任务失败: {e}")
            return jsonify({'error': str(e)}), 500
            
    @app.route('/api/tasks/<task_id>', methods=['PUT'])
    def update_task(task_id):
        """更新任务状态"""
        try:
            data = request.get_json()
            if 'status' not in data:
                return jsonify({'error': '缺少状态字段'}), 400
                
            success = task_model.update_task_status(
                task_id=task_id,
                status=data['status'],
                result=data.get('result'),
                error_message=data.get('error_message')
            )
            
            if success:
                log_model.add_log('INFO', f'任务状态更新为: {data["status"]}', task_id=task_id)
                return jsonify({'success': True, 'message': '任务状态已更新'})
            else:
                return jsonify({'error': '更新失败'}), 400
                
        except Exception as e:
            logger.error(f"更新任务失败: {e}")
            return jsonify({'error': str(e)}), 500
            
    @app.route('/api/tasks/<task_id>', methods=['DELETE'])
    def cancel_task(task_id):
        """取消任务"""
        try:
            success = task_model.update_task_status(task_id, 'cancelled')
            if success:
                log_model.add_log('INFO', '任务已取消', task_id=task_id)
                return jsonify({'success': True, 'message': '任务已取消'})
            else:
                return jsonify({'error': '取消失败'}), 400
                
        except Exception as e:
            logger.error(f"取消任务失败: {e}")
            return jsonify({'error': str(e)}), 500
            
    # ==================== 用户相关API ====================
    
    @app.route('/api/users', methods=['POST'])
    def create_user():
        """创建用户"""
        try:
            data = request.get_json()
            if 'username' not in data:
                return jsonify({'error': '缺少用户名'}), 400
                
            # 检查用户是否已存在
            existing_user = user_model.get_user_by_username(data['username'])
            if existing_user:
                return jsonify({'error': '用户名已存在'}), 400
                
            user_id = user_model.create_user(
                username=data['username'],
                nickname=data.get('nickname')
            )
            
            return jsonify({
                'success': True,
                'message': '用户创建成功',
                'user_id': user_id
            })
            
        except Exception as e:
            logger.error(f"创建用户失败: {e}")
            return jsonify({'error': str(e)}), 500
            
    @app.route('/api/users/<user_id>', methods=['GET'])
    def get_user(user_id):
        """获取用户信息"""
        try:
            user = user_model.get_user(user_id)
            if not user:
                return jsonify({'error': '用户不存在'}), 404
                
            # 转换时间格式
            user['created_at'] = user['created_at'].isoformat()
            user['updated_at'] = user['updated_at'].isoformat()
            if user['last_login']:
                user['last_login'] = user['last_login'].isoformat()
                
            return jsonify({
                'success': True,
                'user': user
            })
            
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return jsonify({'error': str(e)}), 500
            
    # ==================== 日志相关API ====================
    
    @app.route('/api/logs', methods=['GET'])
    def get_logs():
        """获取日志"""
        try:
            level = request.args.get('level')
            task_id = request.args.get('task_id')
            user_id = request.args.get('user_id')
            limit = int(request.args.get('limit', 100))
            
            logs = log_model.get_logs(level, task_id, user_id, limit)
            
            # 转换时间格式
            for log in logs:
                log['timestamp'] = log['timestamp'].isoformat()
                
            return jsonify({
                'success': True,
                'logs': logs
            })
            
        except Exception as e:
            logger.error(f"获取日志失败: {e}")
            return jsonify({'error': str(e)}), 500
