{% extends "base.html" %}

{% block title %}用户管理 - 淘宝天猫秒杀程序{% endblock %}
{% block page_title %}用户管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 操作栏 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createUserModal">
                                    <i class="bi bi-person-plus"></i> 添加用户
                                </button>
                                <button class="btn btn-outline-secondary" onclick="refreshUsers()">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                                <button class="btn btn-outline-danger" onclick="batchDeleteUsers()" id="batchDeleteBtn" style="display: none;">
                                    <i class="bi bi-trash"></i> 批量删除
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <select class="form-select" id="statusFilter" onchange="filterUsers()">
                                    <option value="">全部状态</option>
                                    <option value="active">活跃</option>
                                    <option value="inactive">非活跃</option>
                                </select>
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索用户名..." onkeyup="searchUsers()">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 用户统计 -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-primary" id="totalUsers">0</div>
                    <div class="stats-label">总用户数</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-success" id="activeUsers">0</div>
                    <div class="stats-label">活跃用户</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-info" id="validCookies">0</div>
                    <div class="stats-label">有效Cookie</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-warning" id="todayLogins">0</div>
                    <div class="stats-label">今日登录</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 用户列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-people"></i> 用户列表
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th width="5%">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th width="15%">用户信息</th>
                                    <th width="10%">状态</th>
                                    <th width="10%">Cookie状态</th>
                                    <th width="10%">登录次数</th>
                                    <th width="15%">最后登录</th>
                                    <th width="15%">注册时间</th>
                                    <th width="10%">任务数</th>
                                    <th width="10%">操作</th>
                                </tr>
                            </thead>
                            <tbody id="userTableBody">
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <div class="loading"></div>
                                        <div class="mt-2">正在加载用户数据...</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted">
                            显示 <span id="showingCount">0</span> 条，共 <span id="totalCount">0</span> 条记录
                        </div>
                        <nav>
                            <ul class="pagination pagination-sm mb-0" id="pagination">
                                <!-- 分页按钮将通过JavaScript生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建用户模态框 -->
<div class="modal fade" id="createUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-plus"></i> 添加用户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createUserForm">
                    <div class="mb-3">
                        <label class="form-label">用户名 *</label>
                        <input type="text" class="form-control" id="username" required 
                               placeholder="请输入用户名">
                        <div class="form-text">用户名将用于标识不同的淘宝账号</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">昵称</label>
                        <input type="text" class="form-control" id="nickname" 
                               placeholder="显示昵称（可选）">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">备注</label>
                        <textarea class="form-control" id="userNote" rows="3" 
                                  placeholder="用户备注信息（可选）"></textarea>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>说明：</strong>
                        <ul class="mb-0 mt-2">
                            <li>用户名必须唯一，建议使用淘宝账号相关信息</li>
                            <li>创建用户后需要通过客户端程序上传Cookie</li>
                            <li>只有上传了有效Cookie的用户才能执行秒杀任务</li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitCreateUser()">
                    <i class="bi bi-check-circle"></i> 创建用户
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 用户详情模态框 -->
<div class="modal fade" id="userDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-circle"></i> 用户详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailContent">
                <!-- 用户详情内容将通过JavaScript填充 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-warning" id="editUserBtn" onclick="editCurrentUser()">
                    <i class="bi bi-pencil"></i> 编辑
                </button>
                <button type="button" class="btn btn-danger" id="deleteUserBtn" onclick="deleteCurrentUser()">
                    <i class="bi bi-trash"></i> 删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentUsers = [];
let currentPage = 1;
let pageSize = 20;
let currentUserId = null;

// 页面加载完成后初始化
$(document).ready(function() {
    loadUsers();
    
    // 每60秒刷新一次用户状态
    setInterval(refreshUserStatus, 60000);
});

// 加载用户列表
function loadUsers() {
    const tbody = document.getElementById('userTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="9" class="text-center py-4">
                <div class="loading"></div>
                <div class="mt-2">正在加载用户数据...</div>
            </td>
        </tr>
    `;

    // 调用API获取用户列表
    fetch('/api/users')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.users) {
                currentUsers = data.users;
                renderUsers(data.users);
                updateUserStats(data.users);
            } else {
                currentUsers = [];
                renderUsers([]);
                updateUserStats([]);
            }
        })
        .catch(error => {
            console.error('加载用户列表失败:', error);
            currentUsers = [];
            renderUsers([]);
            updateUserStats([]);
        });
}

// 渲染用户列表
function renderUsers(users) {
    const tbody = document.getElementById('userTableBody');
    
    if (users.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center py-4 text-muted">
                    <i class="bi bi-person-x" style="font-size: 2rem;"></i>
                    <div class="mt-2">暂无用户数据</div>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = users.map(user => `
        <tr data-user-id="${user.user_id}">
            <td>
                <input type="checkbox" class="user-checkbox" value="${user.user_id}" onchange="updateBatchActions()">
            </td>
            <td>
                <div class="user-info">
                    <div class="user-name fw-bold">${user.nickname || user.username}</div>
                    <div class="user-username text-muted small">@${user.username}</div>
                </div>
            </td>
            <td>
                ${createUserStatusBadge(user.status)}
            </td>
            <td>
                ${createCookieStatusBadge(user.has_valid_cookie)}
            </td>
            <td>
                <span class="badge bg-light text-dark">${user.login_count}</span>
            </td>
            <td>
                <div class="time-info">
                    ${user.last_login ? `
                        <div class="small">${Utils.formatDateTime(user.last_login)}</div>
                        <div class="text-muted small">${Utils.formatRelativeTime(user.last_login)}</div>
                    ` : '<span class="text-muted">从未登录</span>'}
                </div>
            </td>
            <td>
                <div class="text-muted small">${Utils.formatRelativeTime(user.created_at)}</div>
            </td>
            <td>
                <span class="badge ${user.task_count > 0 ? 'bg-info' : 'bg-light text-dark'}">${user.task_count}</span>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewUserDetail('${user.user_id}')" title="查看详情">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-outline-warning" onclick="editUser('${user.user_id}')" title="编辑用户">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteUser('${user.user_id}')" title="删除用户">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    // 更新显示计数
    document.getElementById('showingCount').textContent = users.length;
    document.getElementById('totalCount').textContent = users.length;
}

// 创建用户状态徽章
function createUserStatusBadge(status) {
    const statusConfig = {
        active: { class: 'bg-success', text: '活跃' },
        inactive: { class: 'bg-secondary', text: '非活跃' }
    };
    
    const config = statusConfig[status] || { class: 'bg-secondary', text: '未知' };
    return `<span class="badge ${config.class}">${config.text}</span>`;
}

// 创建Cookie状态徽章
function createCookieStatusBadge(hasValidCookie) {
    return hasValidCookie ? 
        '<span class="badge bg-success">有效</span>' : 
        '<span class="badge bg-danger">无效</span>';
}

// 更新用户统计
function updateUserStats(users) {
    const stats = {
        total: users.length,
        active: users.filter(u => u.status === 'active').length,
        validCookies: users.filter(u => u.has_valid_cookie).length,
        todayLogins: users.filter(u => {
            if (!u.last_login) return false;
            const today = new Date().toDateString();
            const loginDate = new Date(u.last_login).toDateString();
            return today === loginDate;
        }).length
    };
    
    document.getElementById('totalUsers').textContent = stats.total;
    document.getElementById('activeUsers').textContent = stats.active;
    document.getElementById('validCookies').textContent = stats.validCookies;
    document.getElementById('todayLogins').textContent = stats.todayLogins;
}

// 提交创建用户
function submitCreateUser() {
    const form = document.getElementById('createUserForm');
    
    const userData = {
        username: document.getElementById('username').value.trim(),
        nickname: document.getElementById('nickname').value.trim()
    };
    
    // 验证表单
    if (!userData.username) {
        Utils.showToast('请输入用户名', 'error');
        return;
    }
    
    // 检查用户名是否已存在
    if (currentUsers.some(u => u.username === userData.username)) {
        Utils.showToast('用户名已存在', 'error');
        return;
    }
    
    // 提交用户
    const submitBtn = event.target;
    const originalContent = Utils.showLoading(submitBtn);
    
    API.createUser(userData)
        .then(response => {
            if (response.success) {
                Utils.showToast('用户创建成功', 'success');
                $('#createUserModal').modal('hide');
                form.reset();
                loadUsers(); // 重新加载用户列表
            }
        })
        .catch(error => {
            Utils.showToast('创建用户失败', 'error');
        })
        .finally(() => {
            Utils.hideLoading(submitBtn, originalContent);
        });
}

// 查看用户详情
function viewUserDetail(userId) {
    currentUserId = userId;
    const user = currentUsers.find(u => u.user_id === userId);
    
    if (!user) {
        Utils.showToast('用户不存在', 'error');
        return;
    }
    
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>用户ID:</td><td>${user.user_id}</td></tr>
                    <tr><td>用户名:</td><td>${user.username}</td></tr>
                    <tr><td>昵称:</td><td>${user.nickname || '未设置'}</td></tr>
                    <tr><td>状态:</td><td>${createUserStatusBadge(user.status)}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>统计信息</h6>
                <table class="table table-sm">
                    <tr><td>登录次数:</td><td>${user.login_count}</td></tr>
                    <tr><td>任务数量:</td><td>${user.task_count}</td></tr>
                    <tr><td>Cookie状态:</td><td>${createCookieStatusBadge(user.has_valid_cookie)}</td></tr>
                </table>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-6">
                <h6>时间信息</h6>
                <table class="table table-sm">
                    <tr><td>注册时间:</td><td>${Utils.formatDateTime(user.created_at)}</td></tr>
                    <tr><td>最后登录:</td><td>${user.last_login ? Utils.formatDateTime(user.last_login) : '从未登录'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>操作记录</h6>
                <div class="alert alert-light">
                    <small class="text-muted">最近操作记录将在此显示</small>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('userDetailContent').innerHTML = content;
    $('#userDetailModal').modal('show');
}

// 编辑用户
function editUser(userId) {
    Utils.showToast('编辑用户功能开发中...', 'info');
}

// 编辑当前用户
function editCurrentUser() {
    if (currentUserId) {
        editUser(currentUserId);
    }
}

// 删除用户
function deleteUser(userId) {
    const user = currentUsers.find(u => u.user_id === userId);
    if (!user) return;
    
    Utils.confirm(`确定要删除用户 "${user.username}" 吗？此操作不可恢复！`, () => {
        // 这里应该调用API删除用户
        Utils.showToast('用户删除功能开发中...', 'info');
    });
}

// 删除当前用户
function deleteCurrentUser() {
    if (currentUserId) {
        $('#userDetailModal').modal('hide');
        setTimeout(() => deleteUser(currentUserId), 300);
    }
}

// 刷新用户
function refreshUsers() {
    loadUsers();
    Utils.showToast('用户列表已刷新', 'info');
}

// 刷新用户状态
function refreshUserStatus() {
    // 这里可以调用API获取最新的用户状态
    console.log('刷新用户状态');
}

// 过滤用户
function filterUsers() {
    const status = document.getElementById('statusFilter').value;
    const filteredUsers = status ? currentUsers.filter(u => u.status === status) : currentUsers;
    renderUsers(filteredUsers);
}

// 搜索用户
function searchUsers() {
    const keyword = document.getElementById('searchInput').value.toLowerCase();
    const filteredUsers = keyword ? 
        currentUsers.filter(u => 
            u.username.toLowerCase().includes(keyword) || 
            (u.nickname && u.nickname.toLowerCase().includes(keyword))
        ) : currentUsers;
    renderUsers(filteredUsers);
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.user-checkbox');
    checkboxes.forEach(cb => cb.checked = selectAll.checked);
    updateBatchActions();
}

// 更新批量操作按钮
function updateBatchActions() {
    const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    
    if (checkedBoxes.length > 0) {
        batchDeleteBtn.style.display = 'inline-block';
    } else {
        batchDeleteBtn.style.display = 'none';
    }
}

// 批量删除用户
function batchDeleteUsers() {
    const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
    const userIds = Array.from(checkedBoxes).map(cb => cb.value);
    
    if (userIds.length === 0) {
        Utils.showToast('请选择要删除的用户', 'warning');
        return;
    }
    
    Utils.confirm(`确定要删除选中的 ${userIds.length} 个用户吗？此操作不可恢复！`, () => {
        Utils.showToast('批量删除功能开发中...', 'info');
    });
}
</script>
{% endblock %}
