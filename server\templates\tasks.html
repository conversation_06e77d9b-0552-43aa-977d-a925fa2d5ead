{% extends "base.html" %}

{% block title %}任务管理 - 淘宝天猫秒杀程序{% endblock %}
{% block page_title %}任务管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 操作栏 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTaskModal">
                                    <i class="bi bi-plus-circle"></i> 创建任务
                                </button>
                                <button class="btn btn-success" onclick="batchCreateTasks()">
                                    <i class="bi bi-collection"></i> 批量创建
                                </button>
                                <button class="btn btn-outline-secondary" onclick="refreshTasks()">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <select class="form-select" id="statusFilter" onchange="filterTasks()">
                                    <option value="">全部状态</option>
                                    <option value="pending">待执行</option>
                                    <option value="running">运行中</option>
                                    <option value="success">成功</option>
                                    <option value="failed">失败</option>
                                    <option value="cancelled">已取消</option>
                                </select>
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索商品URL..." onkeyup="searchTasks()">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 任务统计 -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-primary" id="totalTasks">0</div>
                    <div class="stats-label">总任务数</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-warning" id="pendingTasks">0</div>
                    <div class="stats-label">待执行</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-info" id="runningTasks">0</div>
                    <div class="stats-label">运行中</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-success" id="successTasks">0</div>
                    <div class="stats-label">成功完成</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 任务列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-task"></i> 任务列表
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th width="5%">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th width="30%">商品信息</th>
                                    <th width="10%">用户</th>
                                    <th width="10%">状态</th>
                                    <th width="15%">执行时间</th>
                                    <th width="15%">创建时间</th>
                                    <th width="10%">进度</th>
                                    <th width="5%">操作</th>
                                </tr>
                            </thead>
                            <tbody id="taskTableBody">
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="loading"></div>
                                        <div class="mt-2">正在加载任务数据...</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted">
                            显示 <span id="showingCount">0</span> 条，共 <span id="totalCount">0</span> 条记录
                        </div>
                        <nav>
                            <ul class="pagination pagination-sm mb-0" id="pagination">
                                <!-- 分页按钮将通过JavaScript生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建任务模态框 -->
<div class="modal fade" id="createTaskModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-plus-circle"></i> 创建秒杀任务
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createTaskForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">选择用户 *</label>
                            <select class="form-select" id="taskUserId" required>
                                <option value="">请选择用户</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">任务类型</label>
                            <select class="form-select" id="taskType">
                                <option value="single">单个任务</option>
                                <option value="batch">批量任务</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">商品URL *</label>
                        <input type="url" class="form-control" id="productUrl" required 
                               placeholder="https://detail.tmall.com/item.htm?id=123456&skuId=789">
                        <div class="form-text">请输入淘宝或天猫商品详情页URL</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">执行时间 *</label>
                            <input type="datetime-local" class="form-control" id="scheduledTime" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">购买数量</label>
                            <input type="number" class="form-control" id="quantity" value="1" min="1" max="10">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">备注</label>
                        <textarea class="form-control" id="taskNote" rows="3" placeholder="任务备注信息（可选）"></textarea>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>注意事项：</strong>
                        <ul class="mb-0 mt-2">
                            <li>请确保选择的用户有有效的Cookie</li>
                            <li>执行时间建议设置在商品开售前几秒</li>
                            <li>批量任务会为多个用户创建相同的秒杀任务</li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitCreateTask()">
                    <i class="bi bi-check-circle"></i> 创建任务
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 任务详情模态框 -->
<div class="modal fade" id="taskDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-eye"></i> 任务详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="taskDetailContent">
                <!-- 任务详情内容将通过JavaScript填充 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-danger" id="cancelTaskBtn" onclick="cancelCurrentTask()" style="display: none;">
                    <i class="bi bi-x-circle"></i> 取消任务
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentTasks = [];
let currentPage = 1;
let pageSize = 20;
let currentTaskId = null;

// 页面加载完成后初始化
$(document).ready(function() {
    loadUsers();
    loadTasks();
    
    // 设置默认执行时间为当前时间+1小时
    const now = new Date();
    now.setHours(now.getHours() + 1);
    document.getElementById('scheduledTime').value = now.toISOString().slice(0, 16);
    
    // 每30秒刷新一次任务状态
    setInterval(refreshTaskStatus, 30000);
});

// 加载用户列表
function loadUsers() {
    const userSelect = document.getElementById('taskUserId');
    userSelect.innerHTML = '<option value="">正在加载用户...</option>';

    // 调用API获取用户列表
    fetch('/api/users')
        .then(response => response.json())
        .then(data => {
            userSelect.innerHTML = '<option value="">请选择用户</option>';
            if (data.success && data.users) {
                data.users.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user.user_id;
                    option.textContent = user.nickname || user.username;
                    userSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('加载用户列表失败:', error);
            userSelect.innerHTML = '<option value="">加载失败</option>';
        });
}

// 加载任务列表
function loadTasks() {
    const tbody = document.getElementById('taskTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="8" class="text-center py-4">
                <div class="loading"></div>
                <div class="mt-2">正在加载任务数据...</div>
            </td>
        </tr>
    `;

    // 调用API获取任务列表
    fetch('/api/tasks')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.tasks) {
                currentTasks = data.tasks;
                renderTasks(data.tasks);
                updateTaskStats(data.tasks);
            } else {
                currentTasks = [];
                renderTasks([]);
                updateTaskStats([]);
            }
        })
        .catch(error => {
            console.error('加载任务列表失败:', error);
            currentTasks = [];
            renderTasks([]);
            updateTaskStats([]);
        });
}

// 渲染任务列表
function renderTasks(tasks) {
    const tbody = document.getElementById('taskTableBody');
    
    if (tasks.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4 text-muted">
                    <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                    <div class="mt-2">暂无任务数据</div>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = tasks.map(task => `
        <tr data-task-id="${task.task_id}">
            <td>
                <input type="checkbox" class="task-checkbox" value="${task.task_id}">
            </td>
            <td>
                <div class="task-info">
                    <div class="task-title fw-bold">${extractProductName(task.product_url)}</div>
                    <div class="task-url text-muted small">${task.product_url}</div>
                    <div class="text-muted small">数量: ${task.quantity}</div>
                </div>
            </td>
            <td>
                <span class="badge bg-light text-dark">${task.username || task.user_id}</span>
            </td>
            <td>
                ${Components.createStatusBadge(task.status)}
            </td>
            <td>
                <div class="time-info">
                    <div class="fw-bold">${Utils.formatDateTime(task.scheduled_time)}</div>
                </div>
            </td>
            <td>
                <div class="text-muted small">${Utils.formatRelativeTime(task.created_at)}</div>
            </td>
            <td>
                ${createProgressBar(task)}
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewTaskDetail('${task.task_id}')" title="查看详情">
                        <i class="bi bi-eye"></i>
                    </button>
                    ${task.status === 'pending' ? `
                        <button class="btn btn-outline-danger" onclick="cancelTask('${task.task_id}')" title="取消任务">
                            <i class="bi bi-x-circle"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');
    
    // 更新显示计数
    document.getElementById('showingCount').textContent = tasks.length;
    document.getElementById('totalCount').textContent = tasks.length;
}

// 创建进度条
function createProgressBar(task) {
    const progressMap = {
        pending: { width: 0, class: 'bg-secondary', text: '等待中' },
        running: { width: 50, class: 'bg-info', text: '执行中' },
        success: { width: 100, class: 'bg-success', text: '已完成' },
        failed: { width: 100, class: 'bg-danger', text: '失败' },
        cancelled: { width: 0, class: 'bg-secondary', text: '已取消' }
    };
    
    const progress = progressMap[task.status] || progressMap.pending;
    
    return `
        <div class="progress" style="height: 6px;">
            <div class="progress-bar ${progress.class}" style="width: ${progress.width}%"></div>
        </div>
        <div class="small text-muted mt-1">${progress.text}</div>
    `;
}

// 提取商品名称
function extractProductName(url) {
    if (!url || typeof url !== 'string') {
        return '未知商品';
    }
    const match = url.match(/id=(\d+)/);
    return match ? `商品 ${match[1]}` : '未知商品';
}

// 更新任务统计
function updateTaskStats(tasks) {
    const stats = {
        total: tasks.length,
        pending: tasks.filter(t => t.status === 'pending').length,
        running: tasks.filter(t => t.status === 'running').length,
        success: tasks.filter(t => t.status === 'success').length
    };
    
    document.getElementById('totalTasks').textContent = stats.total;
    document.getElementById('pendingTasks').textContent = stats.pending;
    document.getElementById('runningTasks').textContent = stats.running;
    document.getElementById('successTasks').textContent = stats.success;
}

// 提交创建任务
function submitCreateTask() {
    const form = document.getElementById('createTaskForm');
    const formData = new FormData(form);
    
    const taskData = {
        user_id: document.getElementById('taskUserId').value,
        product_url: document.getElementById('productUrl').value,
        scheduled_time: document.getElementById('scheduledTime').value,
        task_type: document.getElementById('taskType').value,
        quantity: parseInt(document.getElementById('quantity').value)
    };
    
    // 验证表单
    if (!taskData.user_id || !taskData.product_url || !taskData.scheduled_time) {
        Utils.showToast('请填写所有必需字段', 'error');
        return;
    }
    
    // 提交任务
    const submitBtn = event.target;
    const originalContent = Utils.showLoading(submitBtn);
    
    API.createTask(taskData)
        .then(response => {
            if (response.success) {
                Utils.showToast('任务创建成功', 'success');
                $('#createTaskModal').modal('hide');
                form.reset();
                loadTasks(); // 重新加载任务列表
            }
        })
        .catch(error => {
            Utils.showToast('创建任务失败', 'error');
        })
        .finally(() => {
            Utils.hideLoading(submitBtn, originalContent);
        });
}

// 查看任务详情
function viewTaskDetail(taskId) {
    currentTaskId = taskId;
    const task = currentTasks.find(t => t.task_id === taskId);
    
    if (!task) {
        Utils.showToast('任务不存在', 'error');
        return;
    }
    
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>任务ID:</td><td>${task.task_id}</td></tr>
                    <tr><td>用户:</td><td>${task.username || task.user_id}</td></tr>
                    <tr><td>状态:</td><td>${Components.createStatusBadge(task.status)}</td></tr>
                    <tr><td>数量:</td><td>${task.quantity}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>时间信息</h6>
                <table class="table table-sm">
                    <tr><td>执行时间:</td><td>${Utils.formatDateTime(task.scheduled_time)}</td></tr>
                    <tr><td>创建时间:</td><td>${Utils.formatDateTime(task.created_at)}</td></tr>
                    ${task.started_at ? `<tr><td>开始时间:</td><td>${Utils.formatDateTime(task.started_at)}</td></tr>` : ''}
                    ${task.completed_at ? `<tr><td>完成时间:</td><td>${Utils.formatDateTime(task.completed_at)}</td></tr>` : ''}
                </table>
            </div>
        </div>
        <div class="mt-3">
            <h6>商品信息</h6>
            <div class="alert alert-light">
                <strong>商品URL:</strong><br>
                <a href="${task.product_url}" target="_blank">${task.product_url}</a>
            </div>
        </div>
        ${task.error_message ? `
            <div class="mt-3">
                <h6>错误信息</h6>
                <div class="alert alert-danger">${task.error_message}</div>
            </div>
        ` : ''}
    `;
    
    document.getElementById('taskDetailContent').innerHTML = content;
    
    // 显示/隐藏取消按钮
    const cancelBtn = document.getElementById('cancelTaskBtn');
    if (task.status === 'pending') {
        cancelBtn.style.display = 'inline-block';
    } else {
        cancelBtn.style.display = 'none';
    }
    
    $('#taskDetailModal').modal('show');
}

// 取消当前任务
function cancelCurrentTask() {
    if (currentTaskId) {
        cancelTask(currentTaskId);
    }
}

// 刷新任务
function refreshTasks() {
    loadTasks();
    Utils.showToast('任务列表已刷新', 'info');
}

// 刷新任务状态
function refreshTaskStatus() {
    // 只刷新运行中的任务状态
    const runningTasks = currentTasks.filter(t => t.status === 'running');
    if (runningTasks.length > 0) {
        // 这里应该调用API获取最新状态
        console.log('刷新运行中任务状态');
    }
}

// 过滤任务
function filterTasks() {
    const status = document.getElementById('statusFilter').value;
    const filteredTasks = status ? currentTasks.filter(t => t.status === status) : currentTasks;
    renderTasks(filteredTasks);
}

// 搜索任务
function searchTasks() {
    const keyword = document.getElementById('searchInput').value.toLowerCase();
    const filteredTasks = keyword ? 
        currentTasks.filter(t => t.product_url.toLowerCase().includes(keyword)) : 
        currentTasks;
    renderTasks(filteredTasks);
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.task-checkbox');
    checkboxes.forEach(cb => cb.checked = selectAll.checked);
}

// 批量创建任务
function batchCreateTasks() {
    Utils.showToast('批量创建功能开发中...', 'info');
}
</script>
{% endblock %}
