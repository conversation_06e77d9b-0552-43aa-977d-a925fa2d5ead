#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录窗口模块
功能：提供扫码登录界面，控制浏览器进行淘宝登录
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import os
import sys
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC


class LoginWindow:
    """登录窗口类"""
    
    def __init__(self, parent, callback):
        self.parent = parent
        self.callback = callback
        self.driver = None
        self.login_thread = None
        self.is_logging_in = False
        
        self.create_window()
        self.start_login_process()
        
    def create_window(self):
        """创建登录窗口"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("淘宝登录")
        self.window.geometry("500x400")
        self.window.resizable(False, False)
        
        # 设置窗口居中
        self.center_window()
        
        # 设置窗口为模态
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # 创建主框架
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(
            main_frame,
            text="淘宝扫码登录",
            font=("SF Pro Display", 18, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # 状态显示
        self.status_label = ttk.Label(
            main_frame,
            text="正在启动浏览器...",
            font=("SF Pro Display", 12)
        )
        self.status_label.pack(pady=(0, 20))
        
        # 进度条
        self.progress = ttk.Progressbar(
            main_frame,
            mode='indeterminate',
            length=300
        )
        self.progress.pack(pady=(0, 20))
        self.progress.start()
        
        # 说明文本
        instruction_text = """
登录说明：
1. 程序将自动打开Chrome浏览器
2. 浏览器会跳转到淘宝登录页面
3. 请使用手机淘宝扫描二维码登录
4. 登录成功后程序会自动获取Cookie
5. 请不要手动关闭浏览器窗口
        """
        
        instruction_label = ttk.Label(
            main_frame,
            text=instruction_text.strip(),
            font=("SF Pro Display", 10),
            justify=tk.LEFT
        )
        instruction_label.pack(pady=(0, 20))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=(20, 0))
        
        # 取消按钮
        self.cancel_btn = ttk.Button(
            button_frame,
            text="取消登录",
            command=self.cancel_login
        )
        self.cancel_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 重试按钮
        self.retry_btn = ttk.Button(
            button_frame,
            text="重新登录",
            command=self.retry_login,
            state='disabled'
        )
        self.retry_btn.pack(side=tk.LEFT)
        
        # 绑定窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_window_close)
        
    def center_window(self):
        """窗口居中显示"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def get_chrome_path(self):
        """获取Chrome浏览器路径"""
        # 获取项目根目录
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        chrome_path = os.path.join(project_root, "chrome-win64", "chrome.exe")
        chromedriver_path = os.path.join(project_root, "chromedriver-win64", "chromedriver.exe")
        
        return chrome_path, chromedriver_path
        
    def setup_chrome_driver(self):
        """设置Chrome驱动"""
        try:
            chrome_path, chromedriver_path = self.get_chrome_path()
            
            # 检查文件是否存在
            if not os.path.exists(chrome_path):
                raise Exception(f"Chrome浏览器未找到: {chrome_path}")
            if not os.path.exists(chromedriver_path):
                raise Exception(f"ChromeDriver未找到: {chromedriver_path}")
            
            # 设置Chrome选项
            chrome_options = Options()
            chrome_options.binary_location = chrome_path
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 设置用户代理
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            # 创建服务
            service = Service(chromedriver_path)
            
            # 创建驱动
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # 执行脚本隐藏webdriver特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            return True
            
        except Exception as e:
            self.update_status(f"浏览器启动失败: {str(e)}", is_error=True)
            return False
            
    def start_login_process(self):
        """开始登录流程"""
        self.is_logging_in = True
        self.login_thread = threading.Thread(target=self._login_process, daemon=True)
        self.login_thread.start()
        
    def _login_process(self):
        """登录流程（在后台线程中执行）"""
        try:
            # 设置Chrome驱动
            self.update_status("正在启动浏览器...")
            if not self.setup_chrome_driver():
                return
                
            # 访问淘宝登录页面
            self.update_status("正在跳转到登录页面...")
            self.driver.get("https://login.taobao.com/member/login.jhtml")
            
            # 等待页面加载
            time.sleep(3)
            
            # 检查是否需要切换到扫码登录
            try:
                qr_tab = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.CLASS_NAME, "login-links"))
                )
                # 点击扫码登录标签
                qr_links = self.driver.find_elements(By.CSS_SELECTOR, ".login-links a")
                for link in qr_links:
                    if "扫码登录" in link.text:
                        link.click()
                        break
            except:
                pass  # 如果找不到扫码登录链接，可能已经是扫码登录页面
                
            self.update_status("请使用手机淘宝扫描二维码登录...")
            
            # 等待登录成功
            self.wait_for_login()
            
        except Exception as e:
            self.update_status(f"登录过程出错: {str(e)}", is_error=True)
            self.enable_retry()
            
    def wait_for_login(self):
        """等待登录成功"""
        max_wait_time = 300  # 最大等待5分钟
        start_time = time.time()
        
        while self.is_logging_in and (time.time() - start_time) < max_wait_time:
            try:
                # 检查是否登录成功（通过检查是否跳转到了淘宝首页或我的淘宝）
                current_url = self.driver.current_url
                
                if "login.taobao.com" not in current_url or "my.taobao.com" in current_url:
                    # 登录成功，获取Cookie
                    self.update_status("登录成功，正在获取Cookie...")
                    cookies = self.driver.get_cookies()
                    
                    # 转换Cookie格式
                    cookie_dict = {}
                    for cookie in cookies:
                        cookie_dict[cookie['name']] = cookie['value']
                    
                    # 关闭浏览器
                    self.driver.quit()
                    self.driver = None
                    
                    # 调用回调函数
                    self.window.after(0, lambda: self.on_login_success(cookie_dict))
                    return
                    
                # 检查是否出现错误信息
                error_elements = self.driver.find_elements(By.CSS_SELECTOR, ".error, .J_Message")
                if error_elements:
                    for element in error_elements:
                        if element.is_displayed() and element.text.strip():
                            self.update_status(f"登录错误: {element.text}", is_error=True)
                            self.enable_retry()
                            return
                            
            except Exception as e:
                # 浏览器可能被关闭了
                if "chrome not reachable" in str(e).lower():
                    self.update_status("浏览器已关闭", is_error=True)
                    self.enable_retry()
                    return
                    
            time.sleep(2)  # 每2秒检查一次
            
        # 超时
        if self.is_logging_in:
            self.update_status("登录超时，请重试", is_error=True)
            self.enable_retry()
            
    def update_status(self, message, is_error=False):
        """更新状态显示"""
        def update():
            self.status_label.config(text=message)
            if is_error:
                self.status_label.config(foreground='red')
            else:
                self.status_label.config(foreground='black')
                
        self.window.after(0, update)
        
    def enable_retry(self):
        """启用重试按钮"""
        def enable():
            self.progress.stop()
            self.retry_btn.config(state='normal')
            
        self.window.after(0, enable)
        
    def on_login_success(self, cookies):
        """登录成功处理"""
        self.progress.stop()
        self.callback(cookies)
        self.window.destroy()
        
    def retry_login(self):
        """重试登录"""
        self.retry_btn.config(state='disabled')
        self.progress.start()
        self.update_status("正在重新启动浏览器...")
        
        # 关闭之前的浏览器
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
            self.driver = None
            
        # 重新开始登录流程
        self.start_login_process()
        
    def cancel_login(self):
        """取消登录"""
        self.is_logging_in = False
        
        # 关闭浏览器
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
            self.driver = None
            
        self.window.destroy()
        
    def on_window_close(self):
        """窗口关闭事件处理"""
        self.cancel_login()
