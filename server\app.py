#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
淘宝天猫秒杀程序 - Flask后端服务器
功能：提供API接口，处理cookie存储、秒杀任务管理等
作者：T团队
"""

from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import threading
import time
from datetime import datetime, timedelta
import logging
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server.models.database import Database, UserModel, CookieModel, TaskModel, ProductModel, LogModel
from server.core.seckill_engine import SeckillEngine
from server.api.routes import create_api_routes


class TaobaoSeckillServer:
    """淘宝秒杀服务器主类"""
    
    def __init__(self):
        self.app = Flask(__name__, 
                        template_folder='templates',
                        static_folder='static')
        
        # 配置CORS
        CORS(self.app)
        
        # 配置日志
        self.setup_logging()
        
        # 初始化数据库
        self.db = Database()
        self.user_model = UserModel(self.db)
        self.cookie_model = CookieModel(self.db)
        self.task_model = TaskModel(self.db)
        self.product_model = ProductModel(self.db)
        self.log_model = LogModel(self.db)
        
        # 初始化秒杀引擎
        self.seckill_engine = SeckillEngine(
            self.db, self.cookie_model, self.task_model, self.log_model
        )
        
        # 注册路由
        self.setup_routes()
        
        # 启动后台任务
        self.start_background_tasks()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('server.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_routes(self):
        """设置路由"""
        # 创建API路由
        create_api_routes(self.app, self.user_model, self.cookie_model, 
                         self.task_model, self.product_model, self.log_model)
        
        # 主页路由
        @self.app.route('/')
        def index():
            return render_template('index.html')

        # 任务管理页面
        @self.app.route('/tasks')
        def tasks():
            return render_template('tasks.html')

        # 用户管理页面
        @self.app.route('/users')
        def users():
            return render_template('users.html')

        # Cookie管理页面
        @self.app.route('/cookies')
        def cookies():
            return render_template('cookies.html')

        # 系统日志页面
        @self.app.route('/logs')
        def logs():
            return render_template('logs.html')

        # 系统设置页面
        @self.app.route('/settings')
        def settings():
            return render_template('settings.html')

        # 健康检查
        @self.app.route('/api/health')
        def health_check():
            return jsonify({
                'status': 'ok',
                'timestamp': datetime.now().isoformat(),
                'version': '2.0'
            })
            
        # 系统状态
        @self.app.route('/api/status')
        def system_status():
            # 获取任务统计
            pending_tasks = len(self.task_model.get_pending_tasks())
            
            # 获取用户统计
            total_users = self.db.users.count_documents({})
            
            # 获取Cookie统计
            valid_cookies = self.db.cookies.count_documents({
                'is_valid': True,
                'expires_at': {'$gt': datetime.now()}
            })
            
            return jsonify({
                'status': 'running',
                'timestamp': datetime.now().isoformat(),
                'statistics': {
                    'total_users': total_users,
                    'valid_cookies': valid_cookies,
                    'pending_tasks': pending_tasks,
                    'engine_status': self.seckill_engine.get_status()
                }
            })
            
        # 错误处理
        @self.app.errorhandler(404)
        def not_found(error):
            return jsonify({'error': 'Not found'}), 404
            
        @self.app.errorhandler(500)
        def internal_error(error):
            return jsonify({'error': 'Internal server error'}), 500
            
    def start_background_tasks(self):
        """启动后台任务"""
        # 启动秒杀引擎
        engine_thread = threading.Thread(target=self.seckill_engine.start, daemon=True)
        engine_thread.start()
        
        # 启动清理任务
        cleanup_thread = threading.Thread(target=self.cleanup_task, daemon=True)
        cleanup_thread.start()
        
        self.logger.info("后台任务已启动")
        
    def cleanup_task(self):
        """清理任务（定期清理过期数据）"""
        while True:
            try:
                # 清理过期Cookie
                expired_cookies = self.cookie_model.cleanup_expired_cookies()
                if expired_cookies > 0:
                    self.logger.info(f"清理了 {expired_cookies} 个过期Cookie")
                    
                # 清理旧日志
                old_logs = self.log_model.cleanup_old_logs(days=30)
                if old_logs > 0:
                    self.logger.info(f"清理了 {old_logs} 条旧日志")
                    
                # 每小时执行一次清理
                time.sleep(3600)
                
            except Exception as e:
                self.logger.error(f"清理任务出错: {e}")
                time.sleep(300)  # 出错后5分钟重试
                
    def run(self, host='0.0.0.0', port=5000, debug=False):
        """运行服务器"""
        self.logger.info(f"服务器启动在 {host}:{port}")
        self.app.run(host=host, port=port, debug=debug, threaded=True)


if __name__ == '__main__':
    server = TaobaoSeckillServer()
    server.run(debug=True)
