{% extends "base.html" %}

{% block title %}系统设置 - 淘宝天猫秒杀程序{% endblock %}
{% block page_title %}系统设置{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 设置导航 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <ul class="nav nav-pills" id="settingsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="pill" data-bs-target="#general" type="button" role="tab">
                                <i class="bi bi-gear"></i> 基本设置
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="seckill-tab" data-bs-toggle="pill" data-bs-target="#seckill" type="button" role="tab">
                                <i class="bi bi-lightning"></i> 秒杀设置
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="database-tab" data-bs-toggle="pill" data-bs-target="#database" type="button" role="tab">
                                <i class="bi bi-database"></i> 数据库设置
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="security-tab" data-bs-toggle="pill" data-bs-target="#security" type="button" role="tab">
                                <i class="bi bi-shield-check"></i> 安全设置
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="system-tab" data-bs-toggle="pill" data-bs-target="#system" type="button" role="tab">
                                <i class="bi bi-cpu"></i> 系统信息
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 设置内容 -->
    <div class="row">
        <div class="col-12">
            <div class="tab-content" id="settingsTabContent">
                <!-- 基本设置 -->
                <div class="tab-pane fade show active" id="general" role="tabpanel">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-0">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-gear"></i> 基本设置
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="generalSettingsForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">系统名称</label>
                                            <input type="text" class="form-control" id="systemName" value="淘宝天猫秒杀程序">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">服务器端口</label>
                                            <input type="number" class="form-control" id="serverPort" value="5000" min="1000" max="65535">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">日志级别</label>
                                            <select class="form-select" id="logLevel">
                                                <option value="DEBUG">调试 (DEBUG)</option>
                                                <option value="INFO" selected>信息 (INFO)</option>
                                                <option value="WARNING">警告 (WARNING)</option>
                                                <option value="ERROR">错误 (ERROR)</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">时区设置</label>
                                            <select class="form-select" id="timezone">
                                                <option value="Asia/Shanghai" selected>中国标准时间 (UTC+8)</option>
                                                <option value="UTC">协调世界时 (UTC)</option>
                                                <option value="America/New_York">美国东部时间</option>
                                                <option value="Europe/London">英国时间</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">语言设置</label>
                                            <select class="form-select" id="language">
                                                <option value="zh-CN" selected>简体中文</option>
                                                <option value="en-US">English</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enableDebug">
                                                <label class="form-check-label" for="enableDebug">
                                                    启用调试模式
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-primary" onclick="saveGeneralSettings()">
                                        <i class="bi bi-check-circle"></i> 保存设置
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="resetGeneralSettings()">
                                        <i class="bi bi-arrow-clockwise"></i> 重置
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- 秒杀设置 -->
                <div class="tab-pane fade" id="seckill" role="tabpanel">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-0">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-lightning"></i> 秒杀设置
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="seckillSettingsForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">最大工作线程数</label>
                                            <input type="number" class="form-control" id="maxWorkers" value="10" min="1" max="50">
                                            <div class="form-text">建议设置为CPU核心数的2-4倍</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">请求超时时间 (秒)</label>
                                            <input type="number" class="form-control" id="requestTimeout" value="15" min="5" max="60">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">最大重试次数</label>
                                            <input type="number" class="form-control" id="maxRetries" value="3" min="0" max="10">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">重试间隔 (毫秒)</label>
                                            <input type="number" class="form-control" id="retryInterval" value="1000" min="100" max="10000">
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">User-Agent</label>
                                            <textarea class="form-control" id="userAgent" rows="3">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36</textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enableProxy">
                                                <label class="form-check-label" for="enableProxy">
                                                    启用代理支持
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enableConcurrency" checked>
                                                <label class="form-check-label" for="enableConcurrency">
                                                    启用并发执行
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enableFailover" checked>
                                                <label class="form-check-label" for="enableFailover">
                                                    启用故障转移
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i>
                                    <strong>注意：</strong>修改这些设置可能会影响系统性能和稳定性，请谨慎操作。
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-primary" onclick="saveSeckillSettings()">
                                        <i class="bi bi-check-circle"></i> 保存设置
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="resetSeckillSettings()">
                                        <i class="bi bi-arrow-clockwise"></i> 重置
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" onclick="testSeckillSettings()">
                                        <i class="bi bi-play-circle"></i> 测试设置
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- 数据库设置 -->
                <div class="tab-pane fade" id="database" role="tabpanel">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-0">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-database"></i> 数据库设置
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="databaseSettingsForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">MongoDB连接字符串</label>
                                            <input type="text" class="form-control" id="mongoUri" value="mongodb://localhost:27017/">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">数据库名称</label>
                                            <input type="text" class="form-control" id="databaseName" value="taobao_seckill">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">连接池大小</label>
                                            <input type="number" class="form-control" id="poolSize" value="10" min="1" max="100">
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">连接超时 (秒)</label>
                                            <input type="number" class="form-control" id="connectTimeout" value="30" min="5" max="300">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">数据保留天数</label>
                                            <input type="number" class="form-control" id="dataRetentionDays" value="30" min="1" max="365">
                                            <div class="form-text">超过此天数的日志将被自动清理</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enableBackup" checked>
                                                <label class="form-check-label" for="enableBackup">
                                                    启用自动备份
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>数据库连接状态</span>
                                        <span class="badge bg-success" id="dbStatus">已连接</span>
                                    </div>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-primary" onclick="saveDatabaseSettings()">
                                        <i class="bi bi-check-circle"></i> 保存设置
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="testDatabaseConnection()">
                                        <i class="bi bi-wifi"></i> 测试连接
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" onclick="backupDatabase()">
                                        <i class="bi bi-archive"></i> 立即备份
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- 安全设置 -->
                <div class="tab-pane fade" id="security" role="tabpanel">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-0">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-shield-check"></i> 安全设置
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                                <strong>安全提醒：</strong>请妥善保管系统配置信息，避免泄露敏感数据。
                            </div>
                            
                            <form id="securitySettingsForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Cookie加密密钥</label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="encryptionKey" value="your-secret-key-here">
                                                <button class="btn btn-outline-secondary" type="button" onclick="generateEncryptionKey()">
                                                    <i class="bi bi-arrow-clockwise"></i> 生成
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">会话超时 (分钟)</label>
                                            <input type="number" class="form-control" id="sessionTimeout" value="60" min="5" max="1440">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enableHttps">
                                                <label class="form-check-label" for="enableHttps">
                                                    启用HTTPS
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">允许的IP地址</label>
                                            <textarea class="form-control" id="allowedIps" rows="3" placeholder="127.0.0.1&#10;***********/24&#10;留空表示允许所有IP"></textarea>
                                            <div class="form-text">每行一个IP地址或CIDR网段</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enableRateLimit" checked>
                                                <label class="form-check-label" for="enableRateLimit">
                                                    启用访问频率限制
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enableAuditLog" checked>
                                                <label class="form-check-label" for="enableAuditLog">
                                                    启用审计日志
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-primary" onclick="saveSecuritySettings()">
                                        <i class="bi bi-check-circle"></i> 保存设置
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="resetSecuritySettings()">
                                        <i class="bi bi-arrow-clockwise"></i> 重置
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- 系统信息 -->
                <div class="tab-pane fade" id="system" role="tabpanel">
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-white border-0">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-info-circle"></i> 系统信息
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr><td>系统版本:</td><td>v2.0.0</td></tr>
                                        <tr><td>Python版本:</td><td id="pythonVersion">-</td></tr>
                                        <tr><td>Flask版本:</td><td id="flaskVersion">-</td></tr>
                                        <tr><td>启动时间:</td><td id="startTime">-</td></tr>
                                        <tr><td>运行时间:</td><td id="uptime">-</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-4">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-white border-0">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-cpu"></i> 系统资源
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>CPU使用率</span>
                                            <span id="cpuUsage">0%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar" id="cpuProgress" style="width: 0%"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>内存使用率</span>
                                            <span id="memoryUsage">0%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar" id="memoryProgress" style="width: 0%"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>磁盘使用率</span>
                                            <span id="diskUsage">0%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar" id="diskProgress" style="width: 0%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-white border-0">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-tools"></i> 系统操作
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex gap-2 flex-wrap">
                                        <button class="btn btn-outline-info" onclick="refreshSystemInfo()">
                                            <i class="bi bi-arrow-clockwise"></i> 刷新信息
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="restartSystem()">
                                            <i class="bi bi-arrow-repeat"></i> 重启系统
                                        </button>
                                        <button class="btn btn-outline-success" onclick="exportSettings()">
                                            <i class="bi bi-download"></i> 导出配置
                                        </button>
                                        <button class="btn btn-outline-primary" onclick="importSettings()">
                                            <i class="bi bi-upload"></i> 导入配置
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="clearCache()">
                                            <i class="bi bi-trash"></i> 清理缓存
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载完成后初始化
$(document).ready(function() {
    loadSystemInfo();
    
    // 每30秒更新系统资源信息
    setInterval(updateSystemResources, 30000);
});

// 加载系统信息
function loadSystemInfo() {
    // 模拟系统信息
    document.getElementById('pythonVersion').textContent = '3.9.7';
    document.getElementById('flaskVersion').textContent = '2.3.3';
    document.getElementById('startTime').textContent = new Date().toLocaleString();
    
    updateSystemResources();
    updateUptime();
}

// 更新系统资源信息
function updateSystemResources() {
    // 模拟资源使用情况
    const cpu = Math.floor(Math.random() * 30) + 10; // 10-40%
    const memory = Math.floor(Math.random() * 40) + 30; // 30-70%
    const disk = Math.floor(Math.random() * 20) + 20; // 20-40%
    
    document.getElementById('cpuUsage').textContent = cpu + '%';
    document.getElementById('cpuProgress').style.width = cpu + '%';
    document.getElementById('cpuProgress').className = `progress-bar ${cpu > 80 ? 'bg-danger' : cpu > 60 ? 'bg-warning' : 'bg-success'}`;
    
    document.getElementById('memoryUsage').textContent = memory + '%';
    document.getElementById('memoryProgress').style.width = memory + '%';
    document.getElementById('memoryProgress').className = `progress-bar ${memory > 80 ? 'bg-danger' : memory > 60 ? 'bg-warning' : 'bg-success'}`;
    
    document.getElementById('diskUsage').textContent = disk + '%';
    document.getElementById('diskProgress').style.width = disk + '%';
    document.getElementById('diskProgress').className = `progress-bar ${disk > 80 ? 'bg-danger' : disk > 60 ? 'bg-warning' : 'bg-success'}`;
}

// 更新运行时间
function updateUptime() {
    // 模拟运行时间
    const startTime = new Date();
    startTime.setHours(startTime.getHours() - 2); // 假设运行了2小时
    
    const now = new Date();
    const uptime = now - startTime;
    const hours = Math.floor(uptime / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    
    document.getElementById('uptime').textContent = `${hours}小时${minutes}分钟`;
}

// 保存基本设置
function saveGeneralSettings() {
    const settings = {
        systemName: document.getElementById('systemName').value,
        serverPort: document.getElementById('serverPort').value,
        logLevel: document.getElementById('logLevel').value,
        timezone: document.getElementById('timezone').value,
        language: document.getElementById('language').value,
        enableDebug: document.getElementById('enableDebug').checked
    };
    
    Utils.showToast('基本设置保存成功', 'success');
    console.log('保存基本设置:', settings);
}

// 重置基本设置
function resetGeneralSettings() {
    Utils.confirm('确定要重置基本设置吗？', () => {
        document.getElementById('systemName').value = '淘宝天猫秒杀程序';
        document.getElementById('serverPort').value = '5000';
        document.getElementById('logLevel').value = 'INFO';
        document.getElementById('timezone').value = 'Asia/Shanghai';
        document.getElementById('language').value = 'zh-CN';
        document.getElementById('enableDebug').checked = false;
        
        Utils.showToast('基本设置已重置', 'info');
    });
}

// 保存秒杀设置
function saveSeckillSettings() {
    const settings = {
        maxWorkers: document.getElementById('maxWorkers').value,
        requestTimeout: document.getElementById('requestTimeout').value,
        maxRetries: document.getElementById('maxRetries').value,
        retryInterval: document.getElementById('retryInterval').value,
        userAgent: document.getElementById('userAgent').value,
        enableProxy: document.getElementById('enableProxy').checked,
        enableConcurrency: document.getElementById('enableConcurrency').checked,
        enableFailover: document.getElementById('enableFailover').checked
    };
    
    Utils.showToast('秒杀设置保存成功', 'success');
    console.log('保存秒杀设置:', settings);
}

// 重置秒杀设置
function resetSeckillSettings() {
    Utils.confirm('确定要重置秒杀设置吗？', () => {
        document.getElementById('maxWorkers').value = '10';
        document.getElementById('requestTimeout').value = '15';
        document.getElementById('maxRetries').value = '3';
        document.getElementById('retryInterval').value = '1000';
        document.getElementById('userAgent').value = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
        document.getElementById('enableProxy').checked = false;
        document.getElementById('enableConcurrency').checked = true;
        document.getElementById('enableFailover').checked = true;
        
        Utils.showToast('秒杀设置已重置', 'info');
    });
}

// 测试秒杀设置
function testSeckillSettings() {
    Utils.showToast('正在测试秒杀设置...', 'info');
    
    setTimeout(() => {
        Utils.showToast('秒杀设置测试通过', 'success');
    }, 2000);
}

// 保存数据库设置
function saveDatabaseSettings() {
    const settings = {
        mongoUri: document.getElementById('mongoUri').value,
        databaseName: document.getElementById('databaseName').value,
        poolSize: document.getElementById('poolSize').value,
        connectTimeout: document.getElementById('connectTimeout').value,
        dataRetentionDays: document.getElementById('dataRetentionDays').value,
        enableBackup: document.getElementById('enableBackup').checked
    };
    
    Utils.showToast('数据库设置保存成功', 'success');
    console.log('保存数据库设置:', settings);
}

// 测试数据库连接
function testDatabaseConnection() {
    Utils.showToast('正在测试数据库连接...', 'info');
    
    setTimeout(() => {
        const success = Math.random() > 0.2; // 80%成功率
        if (success) {
            document.getElementById('dbStatus').className = 'badge bg-success';
            document.getElementById('dbStatus').textContent = '连接成功';
            Utils.showToast('数据库连接测试成功', 'success');
        } else {
            document.getElementById('dbStatus').className = 'badge bg-danger';
            document.getElementById('dbStatus').textContent = '连接失败';
            Utils.showToast('数据库连接测试失败', 'error');
        }
    }, 2000);
}

// 备份数据库
function backupDatabase() {
    Utils.showToast('正在备份数据库...', 'info');
    
    setTimeout(() => {
        Utils.showToast('数据库备份完成', 'success');
    }, 3000);
}

// 保存安全设置
function saveSecuritySettings() {
    const settings = {
        encryptionKey: document.getElementById('encryptionKey').value,
        sessionTimeout: document.getElementById('sessionTimeout').value,
        allowedIps: document.getElementById('allowedIps').value,
        enableHttps: document.getElementById('enableHttps').checked,
        enableRateLimit: document.getElementById('enableRateLimit').checked,
        enableAuditLog: document.getElementById('enableAuditLog').checked
    };
    
    Utils.showToast('安全设置保存成功', 'success');
    console.log('保存安全设置:', settings);
}

// 重置安全设置
function resetSecuritySettings() {
    Utils.confirm('确定要重置安全设置吗？', () => {
        document.getElementById('encryptionKey').value = 'your-secret-key-here';
        document.getElementById('sessionTimeout').value = '60';
        document.getElementById('allowedIps').value = '';
        document.getElementById('enableHttps').checked = false;
        document.getElementById('enableRateLimit').checked = true;
        document.getElementById('enableAuditLog').checked = true;
        
        Utils.showToast('安全设置已重置', 'info');
    });
}

// 生成加密密钥
function generateEncryptionKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let key = '';
    for (let i = 0; i < 32; i++) {
        key += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    document.getElementById('encryptionKey').value = key;
    Utils.showToast('新的加密密钥已生成', 'success');
}

// 刷新系统信息
function refreshSystemInfo() {
    loadSystemInfo();
    Utils.showToast('系统信息已刷新', 'info');
}

// 重启系统
function restartSystem() {
    Utils.confirm('确定要重启系统吗？这将中断所有正在进行的任务！', () => {
        Utils.showToast('系统重启功能开发中...', 'warning');
    });
}

// 导出配置
function exportSettings() {
    Utils.showToast('配置导出功能开发中...', 'info');
}

// 导入配置
function importSettings() {
    Utils.showToast('配置导入功能开发中...', 'info');
}

// 清理缓存
function clearCache() {
    Utils.confirm('确定要清理系统缓存吗？', () => {
        Utils.showToast('正在清理缓存...', 'info');
        
        setTimeout(() => {
            Utils.showToast('缓存清理完成', 'success');
        }, 2000);
    });
}
</script>
{% endblock %}
