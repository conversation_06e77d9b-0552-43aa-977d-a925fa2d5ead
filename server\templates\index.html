{% extends "base.html" %}

{% block title %}仪表板 - 淘宝天猫秒杀程序{% endblock %}
{% block page_title %}仪表板{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary">
                            <i class="bi bi-people text-white"></i>
                        </div>
                        <div class="ms-3">
                            <div class="stats-number" id="total-users">0</div>
                            <div class="stats-label">总用户数</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success">
                            <i class="bi bi-key text-white"></i>
                        </div>
                        <div class="ms-3">
                            <div class="stats-number" id="valid-cookies">0</div>
                            <div class="stats-label">有效Cookie</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning">
                            <i class="bi bi-clock text-white"></i>
                        </div>
                        <div class="ms-3">
                            <div class="stats-number" id="pending-tasks">0</div>
                            <div class="stats-label">待执行任务</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-info">
                            <i class="bi bi-lightning-charge text-white"></i>
                        </div>
                        <div class="ms-3">
                            <div class="stats-number" id="running-tasks">0</div>
                            <div class="stats-label">运行中任务</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统状态和快速操作 -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-activity"></i> 系统状态
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="status-item mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>秒杀引擎状态</span>
                                    <span class="badge bg-success" id="engine-status">运行中</span>
                                </div>
                            </div>
                            
                            <div class="status-item mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>数据库连接</span>
                                    <span class="badge bg-success" id="db-status">正常</span>
                                </div>
                            </div>
                            
                            <div class="status-item mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>任务队列</span>
                                    <span class="text-muted" id="queue-size">0 个任务</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="status-item mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>工作线程</span>
                                    <span class="text-muted" id="worker-count">10 个线程</span>
                                </div>
                            </div>
                            
                            <div class="status-item mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>成功率</span>
                                    <span class="text-success" id="success-rate">0%</span>
                                </div>
                            </div>
                            
                            <div class="status-item mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>系统运行时间</span>
                                    <span class="text-muted" id="uptime">刚刚启动</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-lightning"></i> 快速操作
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="createTask()">
                            <i class="bi bi-plus-circle"></i> 创建秒杀任务
                        </button>
                        
                        <button class="btn btn-success" onclick="uploadCookie()">
                            <i class="bi bi-upload"></i> 上传Cookie
                        </button>
                        
                        <button class="btn btn-info" onclick="viewLogs()">
                            <i class="bi bi-journal-text"></i> 查看系统日志
                        </button>
                        
                        <button class="btn btn-warning" onclick="systemSettings()">
                            <i class="bi bi-gear"></i> 系统设置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 最近任务和日志 -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history"></i> 最近任务
                    </h5>
                    <a href="/tasks" class="btn btn-sm btn-outline-primary">查看全部</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>商品</th>
                                    <th>状态</th>
                                    <th>时间</th>
                                </tr>
                            </thead>
                            <tbody id="recent-tasks">
                                <tr>
                                    <td colspan="3" class="text-center text-muted">暂无任务数据</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle"></i> 系统日志
                    </h5>
                    <a href="/logs" class="btn btn-sm btn-outline-primary">查看全部</a>
                </div>
                <div class="card-body">
                    <div class="log-container" id="recent-logs">
                        <div class="text-center text-muted">暂无日志数据</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 页面加载完成后初始化
    $(document).ready(function() {
        loadDashboardData();
        
        // 每30秒刷新一次数据
        setInterval(loadDashboardData, 30000);
    });
    
    // 加载仪表板数据
    function loadDashboardData() {
        // 加载系统状态
        $.get('/api/status', function(data) {
            if (data.statistics) {
                $('#total-users').text(data.statistics.total_users || 0);
                $('#valid-cookies').text(data.statistics.valid_cookies || 0);
                $('#pending-tasks').text(data.statistics.pending_tasks || 0);
                $('#running-tasks').text(data.statistics.engine_status?.statistics?.running_tasks || 0);
                
                // 更新引擎状态
                const engineStatus = data.statistics.engine_status;
                if (engineStatus && engineStatus.is_running) {
                    $('#engine-status').removeClass('bg-danger').addClass('bg-success').text('运行中');
                    $('#queue-size').text(engineStatus.queue_size + ' 个任务');
                    $('#worker-count').text(engineStatus.worker_count + ' 个线程');
                    
                    // 计算成功率
                    const stats = engineStatus.statistics;
                    const total = stats.total_tasks || 0;
                    const success = stats.success_tasks || 0;
                    const successRate = total > 0 ? Math.round((success / total) * 100) : 0;
                    $('#success-rate').text(successRate + '%');
                } else {
                    $('#engine-status').removeClass('bg-success').addClass('bg-danger').text('已停止');
                }
            }
        }).fail(function() {
            $('#engine-status').removeClass('bg-success').addClass('bg-danger').text('连接失败');
            $('#db-status').removeClass('bg-success').addClass('bg-danger').text('异常');
        });
        
        // 加载最近任务（这里可以添加具体的API调用）
        // loadRecentTasks();
        
        // 加载最近日志
        // loadRecentLogs();
    }
    
    // 快速操作函数
    function createTask() {
        window.location.href = '/tasks';
    }
    
    function uploadCookie() {
        window.location.href = '/cookies';
    }
    
    function viewLogs() {
        window.location.href = '/logs';
    }
    
    function systemSettings() {
        window.location.href = '/settings';
    }
</script>
{% endblock %}
