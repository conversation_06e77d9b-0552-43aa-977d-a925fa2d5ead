/**
 * 淘宝天猫秒杀程序 - 主要JavaScript文件
 * 功能：提供前端交互功能和API调用
 */

// 全局配置
const CONFIG = {
    API_BASE_URL: '/api',
    REFRESH_INTERVAL: 30000, // 30秒
    TOAST_DURATION: 3000
};

// 工具函数
const Utils = {
    // 格式化时间
    formatDateTime: function(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    },
    
    // 格式化相对时间
    formatRelativeTime: function(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        
        if (days > 0) return `${days}天前`;
        if (hours > 0) return `${hours}小时前`;
        if (minutes > 0) return `${minutes}分钟前`;
        return '刚刚';
    },
    
    // 显示Toast消息
    showToast: function(message, type = 'info') {
        const toastContainer = this.getToastContainer();
        const toast = this.createToast(message, type);
        toastContainer.appendChild(toast);
        
        // 显示动画
        setTimeout(() => toast.classList.add('show'), 100);
        
        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toastContainer.removeChild(toast), 300);
        }, CONFIG.TOAST_DURATION);
    },
    
    // 获取Toast容器
    getToastContainer: function() {
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container';
            document.body.appendChild(container);
        }
        return container;
    },
    
    // 创建Toast元素
    createToast: function(message, type) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        
        const icon = this.getToastIcon(type);
        toast.innerHTML = `
            <div class="toast-content">
                <i class="bi ${icon}"></i>
                <span>${message}</span>
            </div>
        `;
        
        return toast;
    },
    
    // 获取Toast图标
    getToastIcon: function(type) {
        const icons = {
            success: 'bi-check-circle',
            error: 'bi-exclamation-circle',
            warning: 'bi-exclamation-triangle',
            info: 'bi-info-circle'
        };
        return icons[type] || icons.info;
    },
    
    // 显示加载状态
    showLoading: function(element) {
        const originalContent = element.innerHTML;
        element.innerHTML = '<span class="loading"></span> 加载中...';
        element.disabled = true;
        return originalContent;
    },
    
    // 隐藏加载状态
    hideLoading: function(element, originalContent) {
        element.innerHTML = originalContent;
        element.disabled = false;
    },
    
    // 确认对话框
    confirm: function(message, callback) {
        if (window.confirm(message)) {
            callback();
        }
    }
};

// API调用封装
const API = {
    // 基础请求方法
    request: function(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        return fetch(CONFIG.API_BASE_URL + url, finalOptions)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('API请求失败:', error);
                Utils.showToast('网络请求失败: ' + error.message, 'error');
                throw error;
            });
    },
    
    // GET请求
    get: function(url) {
        return this.request(url, { method: 'GET' });
    },
    
    // POST请求
    post: function(url, data) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },
    
    // PUT请求
    put: function(url, data) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },
    
    // DELETE请求
    delete: function(url) {
        return this.request(url, { method: 'DELETE' });
    },
    
    // 系统状态
    getSystemStatus: function() {
        return this.get('/status');
    },
    
    // Cookie相关
    uploadCookies: function(cookies, username) {
        return this.post('/cookies/upload', { cookies, username });
    },
    
    getUserCookies: function(userId) {
        return this.get(`/cookies/${userId}`);
    },
    
    deleteCookies: function(userId) {
        return this.delete(`/cookies/${userId}`);
    },
    
    // 任务相关
    createTask: function(taskData) {
        return this.post('/tasks', taskData);
    },
    
    getTask: function(taskId) {
        return this.get(`/tasks/${taskId}`);
    },
    
    getUserTasks: function(userId, status = null) {
        const url = status ? `/tasks/user/${userId}?status=${status}` : `/tasks/user/${userId}`;
        return this.get(url);
    },
    
    updateTask: function(taskId, updateData) {
        return this.put(`/tasks/${taskId}`, updateData);
    },
    
    cancelTask: function(taskId) {
        return this.delete(`/tasks/${taskId}`);
    },
    
    // 用户相关
    createUser: function(userData) {
        return this.post('/users', userData);
    },
    
    getUser: function(userId) {
        return this.get(`/users/${userId}`);
    },
    
    // 日志相关
    getLogs: function(filters = {}) {
        const params = new URLSearchParams(filters);
        return this.get(`/logs?${params}`);
    }
};

// 页面组件
const Components = {
    // 状态徽章
    createStatusBadge: function(status) {
        const statusConfig = {
            pending: { class: 'bg-warning', text: '待执行' },
            running: { class: 'bg-info', text: '运行中' },
            success: { class: 'bg-success', text: '成功' },
            failed: { class: 'bg-danger', text: '失败' },
            cancelled: { class: 'bg-secondary', text: '已取消' }
        };
        
        const config = statusConfig[status] || { class: 'bg-secondary', text: '未知' };
        return `<span class="badge ${config.class}">${config.text}</span>`;
    },
    
    // 任务行
    createTaskRow: function(task) {
        const productName = this.extractProductName(task.product_url);
        const statusBadge = this.createStatusBadge(task.status);
        const createdTime = Utils.formatRelativeTime(task.created_at);
        
        return `
            <tr data-task-id="${task.task_id}">
                <td>
                    <div class="task-info">
                        <div class="task-title">${productName}</div>
                        <div class="task-url text-muted small">${task.product_url}</div>
                    </div>
                </td>
                <td>${statusBadge}</td>
                <td>
                    <div class="time-info">
                        <div>${Utils.formatDateTime(task.scheduled_time)}</div>
                        <div class="text-muted small">创建于 ${createdTime}</div>
                    </div>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewTask('${task.task_id}')">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${task.status === 'pending' ? `
                            <button class="btn btn-outline-danger" onclick="cancelTask('${task.task_id}')">
                                <i class="bi bi-x-circle"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    },
    
    // 提取商品名称
    extractProductName: function(url) {
        // 简单的URL解析，实际应用中可能需要更复杂的逻辑
        const match = url.match(/\/(\d+)\.htm/);
        return match ? `商品 ${match[1]}` : '未知商品';
    },
    
    // 日志条目
    createLogEntry: function(log) {
        const levelClass = {
            DEBUG: 'text-muted',
            INFO: 'text-info',
            WARNING: 'text-warning',
            ERROR: 'text-danger',
            CRITICAL: 'text-danger fw-bold'
        };
        
        const time = Utils.formatDateTime(log.timestamp);
        const levelCls = levelClass[log.level] || 'text-muted';
        
        return `
            <div class="log-entry">
                <span class="log-time text-muted">[${time}]</span>
                <span class="log-level ${levelCls}">[${log.level}]</span>
                <span class="log-message">${log.message}</span>
            </div>
        `;
    }
};

// 全局函数（供HTML调用）
window.viewTask = function(taskId) {
    // 查看任务详情
    API.getTask(taskId)
        .then(response => {
            if (response.success) {
                showTaskModal(response.task);
            }
        })
        .catch(error => {
            Utils.showToast('获取任务详情失败', 'error');
        });
};

window.cancelTask = function(taskId) {
    Utils.confirm('确定要取消这个任务吗？', () => {
        API.cancelTask(taskId)
            .then(response => {
                if (response.success) {
                    Utils.showToast('任务已取消', 'success');
                    // 刷新任务列表
                    if (typeof refreshTaskList === 'function') {
                        refreshTaskList();
                    }
                }
            })
            .catch(error => {
                Utils.showToast('取消任务失败', 'error');
            });
    });
};

// 显示任务详情模态框
function showTaskModal(task) {
    // 这里可以实现任务详情模态框
    console.log('显示任务详情:', task);
}

// 页面初始化
$(document).ready(function() {
    // 添加Toast样式
    if (!document.getElementById('toast-styles')) {
        const style = document.createElement('style');
        style.id = 'toast-styles';
        style.textContent = `
            .toast-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
            }
            
            .toast {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                margin-bottom: 10px;
                padding: 12px 16px;
                min-width: 300px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            }
            
            .toast.show {
                opacity: 1;
                transform: translateX(0);
            }
            
            .toast-content {
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .toast-success { border-left: 4px solid var(--success-color); }
            .toast-error { border-left: 4px solid var(--danger-color); }
            .toast-warning { border-left: 4px solid var(--warning-color); }
            .toast-info { border-left: 4px solid var(--info-color); }
        `;
        document.head.appendChild(style);
    }
    
    // 初始化侧边栏切换
    $('#sidebarToggle').on('click', function() {
        $('.sidebar').toggleClass('collapsed');
        $('.main-content').toggleClass('expanded');
    });
    
    // 响应式处理
    function handleResize() {
        if (window.innerWidth <= 768) {
            $('.sidebar').addClass('collapsed');
            $('.main-content').addClass('expanded');
        }
    }
    
    $(window).on('resize', handleResize);
    handleResize();
});

// 导出到全局
window.Utils = Utils;
window.API = API;
window.Components = Components;
