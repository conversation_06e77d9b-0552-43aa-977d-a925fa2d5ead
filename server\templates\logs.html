{% extends "base.html" %}

{% block title %}系统日志 - 淘宝天猫秒杀程序{% endblock %}
{% block page_title %}系统日志{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 操作栏 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-secondary" onclick="refreshLogs()">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                                <button class="btn btn-outline-primary" onclick="exportLogs()">
                                    <i class="bi bi-download"></i> 导出日志
                                </button>
                                <button class="btn btn-outline-warning" onclick="clearLogs()">
                                    <i class="bi bi-trash"></i> 清空日志
                                </button>
                                <div class="form-check form-switch ms-3">
                                    <input class="form-check-input" type="checkbox" id="autoRefresh" onchange="toggleAutoRefresh()">
                                    <label class="form-check-label" for="autoRefresh">自动刷新</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <select class="form-select" id="levelFilter" onchange="filterLogs()">
                                    <option value="">全部级别</option>
                                    <option value="DEBUG">调试</option>
                                    <option value="INFO">信息</option>
                                    <option value="WARNING">警告</option>
                                    <option value="ERROR">错误</option>
                                    <option value="CRITICAL">严重错误</option>
                                </select>
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索日志内容..." onkeyup="searchLogs()">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 日志统计 -->
    <div class="row mb-4">
        <div class="col-md-2 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-primary" id="totalLogs">0</div>
                    <div class="stats-label">总日志数</div>
                </div>
            </div>
        </div>
        <div class="col-md-2 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-info" id="infoLogs">0</div>
                    <div class="stats-label">信息</div>
                </div>
            </div>
        </div>
        <div class="col-md-2 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-warning" id="warningLogs">0</div>
                    <div class="stats-label">警告</div>
                </div>
            </div>
        </div>
        <div class="col-md-2 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-danger" id="errorLogs">0</div>
                    <div class="stats-label">错误</div>
                </div>
            </div>
        </div>
        <div class="col-md-2 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-success" id="todayLogs">0</div>
                    <div class="stats-label">今日日志</div>
                </div>
            </div>
        </div>
        <div class="col-md-2 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stats-number text-secondary" id="recentLogs">0</div>
                    <div class="stats-label">最近1小时</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 日志过滤器 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">时间范围</label>
                            <select class="form-select" id="timeRange" onchange="filterByTime()">
                                <option value="all">全部时间</option>
                                <option value="1h">最近1小时</option>
                                <option value="6h">最近6小时</option>
                                <option value="24h" selected>最近24小时</option>
                                <option value="7d">最近7天</option>
                                <option value="30d">最近30天</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">任务ID</label>
                            <input type="text" class="form-control" id="taskIdFilter" placeholder="过滤特定任务" onkeyup="filterLogs()">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">用户ID</label>
                            <input type="text" class="form-control" id="userIdFilter" placeholder="过滤特定用户" onkeyup="filterLogs()">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">显示数量</label>
                            <select class="form-select" id="limitSelect" onchange="filterLogs()">
                                <option value="100" selected>100条</option>
                                <option value="500">500条</option>
                                <option value="1000">1000条</option>
                                <option value="all">全部</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 日志列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-journal-text"></i> 系统日志
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleLogView()">
                            <i class="bi bi-list" id="viewToggleIcon"></i>
                            <span id="viewToggleText">表格视图</span>
                        </button>
                        <button class="btn btn-sm btn-outline-primary" onclick="scrollToTop()">
                            <i class="bi bi-arrow-up"></i> 顶部
                        </button>
                        <button class="btn btn-sm btn-outline-primary" onclick="scrollToBottom()">
                            <i class="bi bi-arrow-down"></i> 底部
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <!-- 表格视图 -->
                    <div id="tableView">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th width="15%">时间</th>
                                        <th width="8%">级别</th>
                                        <th width="40%">消息</th>
                                        <th width="12%">任务ID</th>
                                        <th width="12%">用户ID</th>
                                        <th width="8%">来源</th>
                                        <th width="5%">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="logTableBody">
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="loading"></div>
                                            <div class="mt-2">正在加载日志数据...</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 控制台视图 -->
                    <div id="consoleView" style="display: none;">
                        <div class="log-console" id="logConsole">
                            <div class="text-center py-4">
                                <div class="loading"></div>
                                <div class="mt-2">正在加载日志数据...</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted">
                            显示 <span id="showingCount">0</span> 条，共 <span id="totalCount">0</span> 条记录
                            <span id="lastUpdate" class="ms-3"></span>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-secondary" onclick="loadMoreLogs()" id="loadMoreBtn">
                                <i class="bi bi-arrow-down-circle"></i> 加载更多
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 日志详情模态框 -->
<div class="modal fade" id="logDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-journal-text"></i> 日志详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="logDetailContent">
                <!-- 日志详情内容将通过JavaScript填充 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.log-console {
    background-color: #1e1e1e;
    color: #d4d4d4;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    padding: 20px;
    max-height: 600px;
    overflow-y: auto;
    border-radius: 0 0 12px 12px;
}

.log-entry {
    margin-bottom: 4px;
    padding: 2px 0;
    word-wrap: break-word;
}

.log-entry:hover {
    background-color: rgba(255, 255, 255, 0.05);
    cursor: pointer;
}

.log-timestamp {
    color: #569cd6;
}

.log-level-DEBUG { color: #808080; }
.log-level-INFO { color: #4ec9b0; }
.log-level-WARNING { color: #dcdcaa; }
.log-level-ERROR { color: #f44747; }
.log-level-CRITICAL { color: #ff6b6b; font-weight: bold; }

.log-message {
    color: #d4d4d4;
}

.log-task-id, .log-user-id {
    color: #9cdcfe;
}

.table .log-level {
    font-weight: 600;
    font-size: 12px;
}

.badge-DEBUG { background-color: #6c757d; }
.badge-INFO { background-color: #0dcaf0; }
.badge-WARNING { background-color: #ffc107; color: #000; }
.badge-ERROR { background-color: #dc3545; }
.badge-CRITICAL { background-color: #dc3545; animation: pulse 1s infinite; }

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let currentLogs = [];
let filteredLogs = [];
let currentView = 'table'; // 'table' or 'console'
let autoRefreshInterval = null;
let currentPage = 1;
let pageSize = 100;

// 页面加载完成后初始化
$(document).ready(function() {
    loadLogs();
    
    // 设置默认时间范围为24小时
    document.getElementById('timeRange').value = '24h';
});

// 加载日志列表
function loadLogs() {
    showLoading();
    
    // 模拟日志数据
    setTimeout(() => {
        const logs = generateMockLogs(200);
        currentLogs = logs;
        filteredLogs = logs;
        renderLogs(logs);
        updateLogStats(logs);
        updateLastUpdate();
    }, 1000);
}

// 生成模拟日志数据
function generateMockLogs(count) {
    const levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'];
    const messages = [
        '系统启动成功',
        '用户登录成功',
        'Cookie上传完成',
        '秒杀任务创建',
        '任务执行开始',
        '商品页面访问成功',
        '订单提交成功',
        '网络连接超时',
        'Cookie验证失败',
        '商品库存不足',
        '支付页面跳转失败',
        '数据库连接异常',
        '内存使用率过高',
        '磁盘空间不足'
    ];
    
    const logs = [];
    const now = new Date();
    
    for (let i = 0; i < count; i++) {
        const timestamp = new Date(now - Math.random() * 24 * 60 * 60 * 1000); // 24小时内随机时间
        const level = levels[Math.floor(Math.random() * levels.length)];
        const message = messages[Math.floor(Math.random() * messages.length)];
        
        logs.push({
            id: `log_${i}`,
            timestamp: timestamp.toISOString(),
            level: level,
            message: message,
            task_id: Math.random() > 0.7 ? `task_${Math.floor(Math.random() * 100)}` : null,
            user_id: Math.random() > 0.6 ? `user_${Math.floor(Math.random() * 10)}` : null,
            source: 'system',
            extra_data: {}
        });
    }
    
    return logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
}

// 显示加载状态
function showLoading() {
    const tableBody = document.getElementById('logTableBody');
    const console = document.getElementById('logConsole');
    
    const loadingHtml = `
        <div class="text-center py-4">
            <div class="loading"></div>
            <div class="mt-2">正在加载日志数据...</div>
        </div>
    `;
    
    if (currentView === 'table') {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <div class="loading"></div>
                    <div class="mt-2">正在加载日志数据...</div>
                </td>
            </tr>
        `;
    } else {
        console.innerHTML = loadingHtml;
    }
}

// 渲染日志列表
function renderLogs(logs) {
    if (currentView === 'table') {
        renderTableView(logs);
    } else {
        renderConsoleView(logs);
    }
    
    // 更新计数
    document.getElementById('showingCount').textContent = logs.length;
    document.getElementById('totalCount').textContent = currentLogs.length;
}

// 渲染表格视图
function renderTableView(logs) {
    const tbody = document.getElementById('logTableBody');
    
    if (logs.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4 text-muted">
                    <i class="bi bi-journal-x" style="font-size: 2rem;"></i>
                    <div class="mt-2">暂无日志数据</div>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = logs.map(log => `
        <tr onclick="viewLogDetail('${log.id}')" style="cursor: pointer;">
            <td>
                <div class="time-info">
                    <div class="small">${Utils.formatDateTime(log.timestamp)}</div>
                    <div class="text-muted small">${Utils.formatRelativeTime(log.timestamp)}</div>
                </div>
            </td>
            <td>
                <span class="badge badge-${log.level}">${log.level}</span>
            </td>
            <td>
                <div class="log-message" title="${log.message}">
                    ${log.message.length > 60 ? log.message.substring(0, 60) + '...' : log.message}
                </div>
            </td>
            <td>
                ${log.task_id ? `<code class="small">${log.task_id}</code>` : '<span class="text-muted">-</span>'}
            </td>
            <td>
                ${log.user_id ? `<code class="small">${log.user_id}</code>` : '<span class="text-muted">-</span>'}
            </td>
            <td>
                <span class="badge bg-light text-dark">${log.source}</span>
            </td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); viewLogDetail('${log.id}')" title="查看详情">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// 渲染控制台视图
function renderConsoleView(logs) {
    const console = document.getElementById('logConsole');
    
    if (logs.length === 0) {
        console.innerHTML = `
            <div class="text-center py-4" style="color: #808080;">
                <div>暂无日志数据</div>
            </div>
        `;
        return;
    }
    
    console.innerHTML = logs.map(log => `
        <div class="log-entry" onclick="viewLogDetail('${log.id}')">
            <span class="log-timestamp">[${Utils.formatDateTime(log.timestamp)}]</span>
            <span class="log-level-${log.level}">[${log.level.padEnd(8)}]</span>
            <span class="log-message">${log.message}</span>
            ${log.task_id ? `<span class="log-task-id">[Task:${log.task_id}]</span>` : ''}
            ${log.user_id ? `<span class="log-user-id">[User:${log.user_id}]</span>` : ''}
        </div>
    `).join('');
    
    // 滚动到底部
    console.scrollTop = console.scrollHeight;
}

// 更新日志统计
function updateLogStats(logs) {
    const now = new Date();
    const oneHourAgo = new Date(now - 60 * 60 * 1000);
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    const stats = {
        total: logs.length,
        info: logs.filter(l => l.level === 'INFO').length,
        warning: logs.filter(l => l.level === 'WARNING').length,
        error: logs.filter(l => ['ERROR', 'CRITICAL'].includes(l.level)).length,
        today: logs.filter(l => new Date(l.timestamp) >= todayStart).length,
        recent: logs.filter(l => new Date(l.timestamp) >= oneHourAgo).length
    };
    
    document.getElementById('totalLogs').textContent = stats.total;
    document.getElementById('infoLogs').textContent = stats.info;
    document.getElementById('warningLogs').textContent = stats.warning;
    document.getElementById('errorLogs').textContent = stats.error;
    document.getElementById('todayLogs').textContent = stats.today;
    document.getElementById('recentLogs').textContent = stats.recent;
}

// 更新最后更新时间
function updateLastUpdate() {
    const now = new Date();
    document.getElementById('lastUpdate').textContent = `最后更新: ${Utils.formatDateTime(now.toISOString())}`;
}

// 查看日志详情
function viewLogDetail(logId) {
    const log = currentLogs.find(l => l.id === logId);
    if (!log) return;
    
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>日志ID:</td><td><code>${log.id}</code></td></tr>
                    <tr><td>级别:</td><td><span class="badge badge-${log.level}">${log.level}</span></td></tr>
                    <tr><td>时间:</td><td>${Utils.formatDateTime(log.timestamp)}</td></tr>
                    <tr><td>来源:</td><td>${log.source}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>关联信息</h6>
                <table class="table table-sm">
                    <tr><td>任务ID:</td><td>${log.task_id ? `<code>${log.task_id}</code>` : '无'}</td></tr>
                    <tr><td>用户ID:</td><td>${log.user_id ? `<code>${log.user_id}</code>` : '无'}</td></tr>
                </table>
            </div>
        </div>
        <div class="mt-3">
            <h6>消息内容</h6>
            <div class="alert alert-light">
                <pre class="mb-0">${log.message}</pre>
            </div>
        </div>
        ${Object.keys(log.extra_data).length > 0 ? `
            <div class="mt-3">
                <h6>额外数据</h6>
                <div class="alert alert-light">
                    <pre class="mb-0">${JSON.stringify(log.extra_data, null, 2)}</pre>
                </div>
            </div>
        ` : ''}
    `;
    
    document.getElementById('logDetailContent').innerHTML = content;
    $('#logDetailModal').modal('show');
}

// 切换视图
function toggleLogView() {
    const tableView = document.getElementById('tableView');
    const consoleView = document.getElementById('consoleView');
    const toggleIcon = document.getElementById('viewToggleIcon');
    const toggleText = document.getElementById('viewToggleText');
    
    if (currentView === 'table') {
        currentView = 'console';
        tableView.style.display = 'none';
        consoleView.style.display = 'block';
        toggleIcon.className = 'bi bi-table';
        toggleText.textContent = '表格视图';
        renderConsoleView(filteredLogs);
    } else {
        currentView = 'table';
        tableView.style.display = 'block';
        consoleView.style.display = 'none';
        toggleIcon.className = 'bi bi-list';
        toggleText.textContent = '控制台视图';
        renderTableView(filteredLogs);
    }
}

// 过滤日志
function filterLogs() {
    const level = document.getElementById('levelFilter').value;
    const taskId = document.getElementById('taskIdFilter').value.trim();
    const userId = document.getElementById('userIdFilter').value.trim();
    const limit = document.getElementById('limitSelect').value;
    const searchKeyword = document.getElementById('searchInput').value.toLowerCase().trim();
    
    let filtered = currentLogs;
    
    // 按级别过滤
    if (level) {
        filtered = filtered.filter(log => log.level === level);
    }
    
    // 按任务ID过滤
    if (taskId) {
        filtered = filtered.filter(log => log.task_id && log.task_id.includes(taskId));
    }
    
    // 按用户ID过滤
    if (userId) {
        filtered = filtered.filter(log => log.user_id && log.user_id.includes(userId));
    }
    
    // 按关键词搜索
    if (searchKeyword) {
        filtered = filtered.filter(log => log.message.toLowerCase().includes(searchKeyword));
    }
    
    // 限制数量
    if (limit !== 'all') {
        filtered = filtered.slice(0, parseInt(limit));
    }
    
    filteredLogs = filtered;
    renderLogs(filtered);
}

// 按时间过滤
function filterByTime() {
    const timeRange = document.getElementById('timeRange').value;
    const now = new Date();
    let cutoffTime;
    
    switch (timeRange) {
        case '1h':
            cutoffTime = new Date(now - 60 * 60 * 1000);
            break;
        case '6h':
            cutoffTime = new Date(now - 6 * 60 * 60 * 1000);
            break;
        case '24h':
            cutoffTime = new Date(now - 24 * 60 * 60 * 1000);
            break;
        case '7d':
            cutoffTime = new Date(now - 7 * 24 * 60 * 60 * 1000);
            break;
        case '30d':
            cutoffTime = new Date(now - 30 * 24 * 60 * 60 * 1000);
            break;
        default:
            cutoffTime = null;
    }
    
    if (cutoffTime) {
        currentLogs = currentLogs.filter(log => new Date(log.timestamp) >= cutoffTime);
    }
    
    filterLogs();
}

// 搜索日志
function searchLogs() {
    filterLogs();
}

// 刷新日志
function refreshLogs() {
    loadLogs();
    Utils.showToast('日志已刷新', 'info');
}

// 切换自动刷新
function toggleAutoRefresh() {
    const autoRefresh = document.getElementById('autoRefresh');
    
    if (autoRefresh.checked) {
        autoRefreshInterval = setInterval(refreshLogs, 10000); // 每10秒刷新
        Utils.showToast('已启用自动刷新', 'info');
    } else {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
        }
        Utils.showToast('已关闭自动刷新', 'info');
    }
}

// 导出日志
function exportLogs() {
    Utils.showToast('日志导出功能开发中...', 'info');
}

// 清空日志
function clearLogs() {
    Utils.confirm('确定要清空所有日志吗？此操作不可恢复！', () => {
        Utils.showToast('日志清空功能开发中...', 'info');
    });
}

// 加载更多日志
function loadMoreLogs() {
    Utils.showToast('加载更多功能开发中...', 'info');
}

// 滚动到顶部
function scrollToTop() {
    if (currentView === 'console') {
        document.getElementById('logConsole').scrollTop = 0;
    } else {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
}

// 滚动到底部
function scrollToBottom() {
    if (currentView === 'console') {
        const console = document.getElementById('logConsole');
        console.scrollTop = console.scrollHeight;
    } else {
        window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
    }
}

// 页面卸载时清理定时器
$(window).on('beforeunload', function() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
});
</script>
{% endblock %}
