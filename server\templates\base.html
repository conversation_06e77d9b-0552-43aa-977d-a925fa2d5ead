<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}淘宝天猫秒杀程序{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 侧边导航栏 -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h4><i class="bi bi-lightning-charge"></i> 秒杀系统</h4>
        </div>
        
        <ul class="sidebar-menu">
            <li class="menu-item">
                <a href="/" class="menu-link {% if request.endpoint == 'index' %}active{% endif %}">
                    <i class="bi bi-house"></i>
                    <span>仪表板</span>
                </a>
            </li>
            
            <li class="menu-item">
                <a href="/tasks" class="menu-link {% if request.endpoint == 'tasks' %}active{% endif %}">
                    <i class="bi bi-list-task"></i>
                    <span>任务管理</span>
                </a>
            </li>
            
            <li class="menu-item">
                <a href="/users" class="menu-link {% if request.endpoint == 'users' %}active{% endif %}">
                    <i class="bi bi-people"></i>
                    <span>用户管理</span>
                </a>
            </li>
            
            <li class="menu-item">
                <a href="/cookies" class="menu-link {% if request.endpoint == 'cookies' %}active{% endif %}">
                    <i class="bi bi-key"></i>
                    <span>Cookie管理</span>
                </a>
            </li>
            
            <li class="menu-item">
                <a href="/logs" class="menu-link {% if request.endpoint == 'logs' %}active{% endif %}">
                    <i class="bi bi-journal-text"></i>
                    <span>系统日志</span>
                </a>
            </li>
            
            <li class="menu-item">
                <a href="/settings" class="menu-link {% if request.endpoint == 'settings' %}active{% endif %}">
                    <i class="bi bi-gear"></i>
                    <span>系统设置</span>
                </a>
            </li>
        </ul>
        
        <div class="sidebar-footer">
            <div class="system-status">
                <div class="status-item">
                    <span class="status-dot status-online"></span>
                    <span>系统运行中</span>
                </div>
                <div class="status-time" id="current-time"></div>
            </div>
        </div>
    </nav>
    
    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 顶部导航栏 -->
        <header class="top-navbar">
            <div class="navbar-content">
                <div class="navbar-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <h5 class="page-title mb-0">{% block page_title %}仪表板{% endblock %}</h5>
                </div>
                
                <div class="navbar-right">
                    <div class="navbar-item">
                        <span class="text-muted">服务器时间：</span>
                        <span id="server-time"></span>
                    </div>
                    
                    <div class="navbar-item">
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle"></i> 管理员
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/profile"><i class="bi bi-person"></i> 个人资料</a></li>
                                <li><a class="dropdown-item" href="/settings"><i class="bi bi-gear"></i> 系统设置</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/logout"><i class="bi bi-box-arrow-right"></i> 退出登录</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- 页面内容 -->
        <div class="page-content">
            {% block content %}{% endblock %}
        </div>
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
    
    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            document.getElementById('current-time').textContent = timeString;
            document.getElementById('server-time').textContent = timeString;
        }
        
        // 每秒更新时间
        setInterval(updateTime, 1000);
        updateTime();
        
        // 侧边栏切换
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            document.querySelector('.sidebar').classList.toggle('collapsed');
            document.querySelector('.main-content').classList.toggle('expanded');
        });
    </script>
</body>
</html>
