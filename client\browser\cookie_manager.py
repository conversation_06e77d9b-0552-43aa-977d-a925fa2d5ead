#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie管理模块
功能：保存、加载、清除Cookie数据
"""

import json
import os
from datetime import datetime, timedelta


class CookieManager:
    """Cookie管理器"""
    
    def __init__(self):
        self.cookie_file = "taobao_cookies.json"
        
    def save_cookies(self, cookies):
        """保存Cookie到文件"""
        try:
            cookie_data = {
                "cookies": cookies,
                "timestamp": datetime.now().isoformat(),
                "expires": (datetime.now() + timedelta(days=7)).isoformat()  # 7天过期
            }
            
            with open(self.cookie_file, 'w', encoding='utf-8') as f:
                json.dump(cookie_data, f, ensure_ascii=False, indent=2)
                
            return True
            
        except Exception as e:
            print(f"保存Cookie失败: {e}")
            return False
            
    def load_cookies(self):
        """从文件加载Cookie"""
        try:
            if not os.path.exists(self.cookie_file):
                return None
                
            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)
                
            # 检查是否过期
            expires = datetime.fromisoformat(cookie_data.get("expires", ""))
            if datetime.now() > expires:
                print("Cookie已过期")
                self.clear_cookies()
                return None
                
            return cookie_data.get("cookies", {})
            
        except Exception as e:
            print(f"加载Cookie失败: {e}")
            return None
            
    def clear_cookies(self):
        """清除Cookie文件"""
        try:
            if os.path.exists(self.cookie_file):
                os.remove(self.cookie_file)
            return True
        except Exception as e:
            print(f"清除Cookie失败: {e}")
            return False
            
    def is_cookies_valid(self):
        """检查Cookie是否有效"""
        cookies = self.load_cookies()
        if not cookies:
            return False
            
        # 检查必要的Cookie字段
        required_cookies = ['_tb_token_', 'cookie2', 't']
        for cookie_name in required_cookies:
            if cookie_name not in cookies:
                return False
                
        return True
        
    def get_cookie_info(self):
        """获取Cookie信息"""
        try:
            if not os.path.exists(self.cookie_file):
                return None
                
            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)
                
            return {
                "timestamp": cookie_data.get("timestamp"),
                "expires": cookie_data.get("expires"),
                "cookie_count": len(cookie_data.get("cookies", {}))
            }
            
        except Exception as e:
            print(f"获取Cookie信息失败: {e}")
            return None
